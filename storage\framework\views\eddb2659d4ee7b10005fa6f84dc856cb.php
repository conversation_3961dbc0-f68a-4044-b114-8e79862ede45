<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title'); ?> - GRADIS</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #475569;
            --success-color: #16a34a;
            --warning-color: #ca8a04;
            --danger-color: #dc2626;
            --light-color: #f1f5f9;
            --dark-color: #0f172a;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
            min-height: 100vh;
            position: relative;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background: var(--dark-color);
            color: white;
            transition: all 0.3s ease;
            z-index: 100;
            overflow-y: auto;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
            }

            .sidebar.collapsed {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
                padding: 1rem;
            }

            .top-navbar {
                left: 0 !important;
            }

            .admin-footer {
                left: 0 !important;
            }

            .sidebar-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .mobile-close {
                display: block;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
            }
        }

        .sidebar-header {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mobile-close {
            display: none;
        }

        .sidebar-menu {
            padding: 1rem;
        }

        .sidebar-title {
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            color: #64748b;
            padding: 1rem 0 0.5rem;
            margin: 0;
        }

        .menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-item {
            margin: 0.2rem 0;
        }

        .sidebar-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 1rem;
            color: #e2e8f0;
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .sidebar-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-link i {
            font-size: 1.2rem;
            margin-right: 0.8rem;
        }

        .sidebar-item.active .sidebar-link {
            background: var(--primary-color);
            color: white;
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            min-height: calc(100vh - 60px);
            transition: all 0.3s ease;
            padding: 1rem;
            margin-top: 60px;
            padding-bottom: 80px; /* Espace pour le footer */
        }
        
        .main-content.expanded {
            margin-left: 70px;
        }

        @media (max-width: 576px) {
            .main-content {
                padding: 0.5rem;
            }

            .top-navbar {
                padding: 0 0.5rem;
            }

            .dropdown-menu {
                position: fixed !important;
                top: 60px !important;
                right: 0 !important;
                left: 0 !important;
                width: 100%;
                border-radius: 0;
                border-top: none;
            }
        }

        /* Top Navbar */
        .top-navbar {
            position: fixed;
            top: 0;
            right: 0;
            left: 250px;
            height: 60px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1030;
            padding: 0 1.5rem;
            transition: all 0.3s ease;
        }
        
        .top-navbar.expanded {
            left: 70px;
        }

        @media (max-width: 576px) {
            .navbar .container-fluid {
                padding: 0;
            }

            .navbar .dropdown-toggle {
                padding: 0.5rem;
            }

            .navbar .btn-link {
                padding: 0.5rem;
            }
        }

        /* Footer */
        .admin-footer {
            position: fixed;
            bottom: 0;
            right: 0;
            left: 250px;
            background: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .admin-footer.expanded {
            left: 70px;
        }

        /* Flash Messages */
        .alert {
            margin-bottom: 1rem;
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .alert-success {
            background-color: #ecfdf5;
            color: #065f46;
            border-left: 4px solid #059669;
        }
        
        .alert-danger {
            background-color: #fef2f2;
            color: #991b1b;
            border-left: 4px solid #dc2626;
        }
        
        .alert-warning {
            background-color: #fffbeb;
            color: #92400e;
            border-left: 4px solid #d97706;
        }
        
        .alert-info {
            background-color: #eff6ff;
            color: #1e40af;
            border-left: 4px solid #3b82f6;
        }
        
        .btn-close {
            opacity: 0.5;
            transition: opacity 0.2s ease;
        }
        
        .btn-close:hover {
            opacity: 1;
        }
    </style>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Sidebar -->
    <?php echo $__env->make('layouts.partials.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Top Navbar -->
    <nav class="navbar top-navbar">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <button class="btn btn-link text-dark" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="d-flex align-items-center">
                <div class="dropdown">
                    <button class="btn btn-link text-dark dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-user me-2"></i><?php echo e(Auth::user()->name); ?>

                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a class="dropdown-item" href="<?php echo e(route('admin.profile.edit')); ?>">
                                <i class="fas fa-user-edit me-2"></i>Profil
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php if(is_array(session('success'))): ?>
                    <?php $__currentLoopData = session('success'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo e($message); ?><br>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <?php echo e(session('success')); ?>

                <?php endif; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Footer -->
    <div class="admin-footer" id="adminFooter">
        &copy; <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Custom Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle Sidebar
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileSidebarClose = document.getElementById('mobileSidebarClose');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.main-content');
            const topNavbar = document.querySelector('.top-navbar');
            const adminFooter = document.getElementById('adminFooter');

            function toggleSidebar() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
                topNavbar.classList.toggle('expanded');
                adminFooter.classList.toggle('expanded');
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            if (mobileSidebarClose) {
                mobileSidebarClose.addEventListener('click', toggleSidebar);
            }

            // Fermer le menu sur mobile lors du clic sur un lien
            const sidebarLinks = document.querySelectorAll('.sidebar-link');
            if (window.innerWidth <= 768) {
                sidebarLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        if (sidebar.classList.contains('collapsed')) {
                            toggleSidebar();
                        }
                    });
                });
            }

            // Gestion du comportement responsive du dropdown
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            function adjustDropdownPosition() {
                if (window.innerWidth <= 576) {
                    dropdownMenus.forEach(menu => {
                        menu.style.position = 'fixed';
                        menu.style.top = '60px';
                        menu.style.right = '0';
                        menu.style.left = '0';
                        menu.style.width = '100%';
                    });
                } else {
                    dropdownMenus.forEach(menu => {
                        menu.style = '';
                    });
                }
            }

            window.addEventListener('resize', adjustDropdownPosition);
            adjustDropdownPosition();

            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize Bootstrap popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/layouts/admin.blade.php ENDPATH**/ ?>