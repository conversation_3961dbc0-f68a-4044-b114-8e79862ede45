<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Paramètres de comptabilité
        $accountingSettings = [
            [
                'key' => 'default_payment_terms',
                'value' => json_encode(30),
                'group' => 'accounting',
                'type' => 'integer',
                'description' => 'Délai de paiement par défaut (en jours)'
            ],
            [
                'key' => 'default_currency',
                'value' => json_encode('FCFA'),
                'group' => 'accounting',
                'type' => 'string',
                'description' => 'Devise par défaut'
            ],
            [
                'key' => 'vat_rate',
                'value' => json_encode(18),
                'group' => 'accounting',
                'type' => 'integer',
                'description' => 'Taux de TVA (%)'
            ],
            [
                'key' => 'invoice_prefix',
                'value' => json_encode('FAC-'),
                'group' => 'accounting',
                'type' => 'string',
                'description' => 'Préfixe des numéros de facture'
            ]
        ];

        foreach ($accountingSettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
