@extends('layouts.admin_minimal')

@section('title', 'Créer un utilisateur')

@section('styles')
<style>
    /* FORCER L'APPLICATION DES STYLES - PRIORITÉ ABSOLUE */

    /* Variables CSS modernes et améliorées */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
        --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
        --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        --info-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);

        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-bg-dark: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-border-strong: rgba(255, 255, 255, 0.3);

        --shadow-soft: 0 20px 40px rgba(0, 0, 0, 0.1);
        --shadow-strong: 0 25px 50px rgba(0, 0, 0, 0.15);
        --shadow-hover: 0 30px 60px rgba(0, 0, 0, 0.2);
        --shadow-focus: 0 0 0 4px rgba(79, 70, 229, 0.1);

        --border-radius: 20px;
        --border-radius-small: 12px;
        --border-radius-large: 24px;

        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

        --text-primary: #1a1a1a;
        --text-secondary: rgba(0, 0, 0, 0.7);
        --text-muted: rgba(0, 0, 0, 0.6);
        --text-light: #f9fafb;

        --spacing-xs: 0.5rem;
        --spacing-sm: 1rem;
        --spacing-md: 1.5rem;
        --spacing-lg: 2rem;
        --spacing-xl: 3rem;
    }

    /* FORCER LE STYLE DU BODY ET MAIN-CONTENT */
    body {
        background: var(--primary-gradient) !important;
        min-height: 100vh !important;
        font-family: 'Inter', 'Segoe UI', system-ui, sans-serif !important;
        overflow-x: hidden !important;
        position: relative !important;
    }



    /* Particules d'arrière-plan animées */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
        animation: particleFloat 20s ease-in-out infinite;
        z-index: -1;
    }

    @keyframes particleFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(120deg); }
        66% { transform: translateY(20px) rotate(240deg); }
    }

    /* Container principal avec effet glassmorphism amélioré */
    .wizard-container {
        max-width: 1000px;
        margin: var(--spacing-lg) auto;
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 1px solid var(--glass-border-strong);
        border-radius: var(--border-radius-large);
        box-shadow: var(--shadow-strong);
        overflow: hidden;
        position: relative;
        transition: var(--transition-slow);
    }

    .wizard-container:hover {
        box-shadow: var(--shadow-hover);
        transform: translateY(-2px);
    }

    .wizard-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--secondary-gradient);
        z-index: 1;
        animation: progressShimmer 3s ease-in-out infinite;
    }

    @keyframes progressShimmer {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    /* Header moderne avec icône animée et effets visuels */
    .wizard-header {
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
        padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .wizard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(79, 70, 229, 0.05) 0%, transparent 70%);
        animation: headerPulse 4s ease-in-out infinite;
        z-index: 0;
    }

    @keyframes headerPulse {
        0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
        50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
    }

    .wizard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .wizard-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.75rem;
        color: var(--text-primary);
        position: relative;
        z-index: 2;
    }

    .wizard-subtitle {
        color: var(--text-secondary);
        font-size: 1.125rem;
        font-weight: 500;
        position: relative;
        z-index: 2;
        margin-bottom: 2rem;
    }

    .wizard-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--secondary-gradient);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto 1.5rem;
        box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
        position: relative;
        z-index: 2;
        animation: float 3s ease-in-out infinite;
    }

    /* Bouton de retour flottant */
    .floating-back-btn {
        position: fixed;
        top: 2rem;
        left: 2rem;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        color: var(--text-primary);
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        font-size: 0.9rem;
        transition: var(--transition);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .floating-back-btn:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        color: #4f46e5;
    }

    .floating-back-btn i {
        font-size: 1rem;
    }

    /* Indicateur de progression */
    .progress-indicator {
        position: relative;
        z-index: 2;
    }

    .progress-bar {
        width: 100%;
        max-width: 300px;
        height: 6px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        margin: 0 auto 0.75rem;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #34d399);
        border-radius: 3px;
        width: 25%;
        transition: width 0.5s ease;
    }

    .progress-text {
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
        text-align: center;
    }

    /* Stepper moderne avec animations */
    .step-progress {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2.5rem 0;
        padding: 0 2rem;
        position: relative;
    }

    .step-progress::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 10%;
        right: 10%;
        height: 2px;
        background: linear-gradient(90deg, #e5e7eb 0%, #d1d5db 100%);
        transform: translateY(-50%);
        z-index: 1;
    }

    .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;
        max-width: 180px;
        z-index: 2;
    }

    .step-number {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        color: var(--text-muted);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.4rem;
        margin-bottom: 1rem;
        transition: var(--transition);
        position: relative;
        border: 4px solid transparent;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        cursor: pointer;
    }

    .step-number::before {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border-radius: 50%;
        background: linear-gradient(135deg, transparent, transparent);
        transition: var(--transition);
        z-index: -1;
    }
        z-index: 2;
    }

    .step-number::before {
        content: '';
        position: absolute;
        inset: -3px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e5e7eb, #d1d5db);
        z-index: -1;
        transition: var(--transition);
    }

    .step-item.active .step-number {
        background: var(--secondary-gradient);
        color: white;
        transform: scale(1.15);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
        animation: pulse 2s infinite;
    }

    .step-item.active .step-number::before {
        background: var(--secondary-gradient);
    }

    @keyframes pulse {
        0%, 100% { box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4); }
        50% { box-shadow: 0 8px 35px rgba(79, 70, 229, 0.6); }
    }

    .step-item.completed .step-number {
        background: var(--success-gradient);
        color: white;
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
    }

    .step-item.completed .step-number::before {
        background: var(--success-gradient);
    }

    .step-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.6) !important;
        text-align: center;
        line-height: 1.4;
        transition: var(--transition);
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    }

    .step-item.active .step-title {
        color: #4f46e5 !important;
        font-weight: 700;
        transform: translateY(-2px);
    }

    .step-item.completed .step-title {
        color: #10b981 !important;
        font-weight: 600;
    }

    .step-connector {
        position: absolute;
        top: 25px;
        left: 50%;
        right: -50%;
        height: 2px;
        background: #e5e7eb;
        z-index: 1;
    }

    .step-item:last-child .step-connector {
        display: none;
    }

    .step-item.completed .step-connector {
        background: #10b981;
    }

    /* Contenu du wizard moderne */
    .wizard-content {
        padding: 0;
        background: transparent;
    }

    .step-content {
        display: none;
        padding: 3rem 2.5rem;
        min-height: 500px;
        position: relative;
    }

    .step-content.active {
        display: block;
        animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.98);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Header des étapes avec design moderne */
    .step-header {
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
    }

    .step-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--secondary-gradient);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin: 0 auto 1.5rem;
        box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
        position: relative;
        transition: var(--transition);
    }

    .step-icon:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4);
    }

    .step-icon::before {
        content: '';
        position: absolute;
        inset: -4px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(99, 102, 241, 0.1));
        z-index: -1;
        animation: rotate 8s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .step-icon:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4);
    }

    .step-title {
        font-size: 2rem;
        font-weight: 800;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        letter-spacing: -0.025em;
    }

    .step-description {
        color: var(--text-secondary);
        font-size: 1.125rem;
        font-weight: 500;
        max-width: 500px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Formulaires modernes avec floating labels */
    .form-group {
        margin-bottom: 2rem;
        position: relative;
    }

    .form-floating {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-control-modern {
        width: 100%;
        padding: 1rem 1rem 1rem 3rem;
        border: 2px solid #e5e7eb;
        border-radius: 15px;
        font-size: 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .form-control-modern:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        font-size: 1.125rem;
        z-index: 2;
        transition: var(--transition);
    }

    .form-floating:focus-within .input-icon {
        color: #007bff !important;
        transform: translateY(-50%) scale(1.1);
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 3rem;
        height: 100%;
        padding: 1rem 0;
        pointer-events: none;
        border: 1px solid transparent;
        transform-origin: 0 0;
        transition: var(--transition);
        color: var(--text-muted);
        font-weight: 500;
    }

    .form-floating > .form-control-modern:focus ~ label,
    .form-floating > .form-control-modern:not(:placeholder-shown) ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(-0.15rem);
        color: #4f46e5;
        font-weight: 600;
    }

    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .form-control.is-invalid {
        border-color: #ef4444;
    }

    .invalid-feedback {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Sélection de rôles modernes */
    .roles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0 3rem 0;
    }

    .role-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 2rem;
        cursor: pointer;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    }

    .role-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #e5e7eb, #d1d5db);
        transition: var(--transition);
    }

    .role-card:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
        border-color: rgba(79, 70, 229, 0.3);
    }

    .role-card:hover::before {
        background: var(--secondary-gradient);
    }

    .role-card.selected {
        border-color: #4f46e5;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(99, 102, 241, 0.02) 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(79, 70, 229, 0.15);
    }

    .role-card.selected::before {
        background: var(--secondary-gradient);
        height: 5px;
    }

    .role-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .role-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: var(--secondary-gradient);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        transition: var(--transition);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
    }

    .role-card:hover .role-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 6px 20px rgba(79, 70, 229, 0.3);
    }

    .role-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        text-transform: capitalize;
    }

    .role-description {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin: 0;
        line-height: 1.4;
    }
        background: var(--secondary-gradient);
        transform: scaleX(0);
        transition: var(--transition);
    }

    .role-card:hover {
        border-color: rgba(79, 70, 229, 0.3);
        box-shadow: 0 15px 40px rgba(79, 70, 229, 0.15);
        transform: translateY(-8px) scale(1.02);
    }

    .role-card:hover::before {
        transform: scaleX(1);
    }

    .role-card.selected {
        border-color: #4f46e5;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
        box-shadow: 0 20px 50px rgba(79, 70, 229, 0.25);
        transform: translateY(-5px) scale(1.03);
    }

    .role-card.selected::before {
        transform: scaleX(1);
        background: var(--success-gradient);
    }

    .role-card.selected::after {
        content: '✓';
        position: absolute;
        top: 1rem;
        right: 1rem;
        width: 30px;
        height: 30px;
        background: var(--success-gradient);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.875rem;
        animation: checkmark 0.3s ease-out;
    }

    @keyframes checkmark {
        from { transform: scale(0) rotate(180deg); opacity: 0; }
        to { transform: scale(1) rotate(0deg); opacity: 1; }
    }

    .role-header {
        display: flex;
        align-items: center;
        gap: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .role-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: var(--secondary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
        transition: var(--transition);
    }

    .role-card:hover .role-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 12px 30px rgba(79, 70, 229, 0.4);
    }

    .role-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
        letter-spacing: -0.025em;
    }

    .role-description {
        color: var(--text-secondary);
        font-size: 0.95rem;
        margin: 0;
        line-height: 1.5;
    }

    /* Boutons modernes avec effets */
    .btn {
        padding: 1rem 2.5rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1rem;
        border: none;
        cursor: pointer;
        transition: var(--transition);
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        position: relative;
        overflow: hidden;
        text-decoration: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn-primary {
        background: var(--secondary-gradient);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
    }

    .btn-primary:active {
        transform: translateY(-1px) scale(1.02);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        color: var(--text-secondary);
        border: 2px solid #e2e8f0;
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    /* Navigation des étapes */
    .step-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* Indicateur de force du mot de passe amélioré */
    .password-strength-container {
        margin-top: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    .password-strength-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
    }

    .password-strength-bar {
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 0.5rem;
        position: relative;
    }

    .password-strength-fill {
        height: 100%;
        border-radius: 4px;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .password-strength-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .password-strength-fill.weak {
        background: var(--danger-gradient);
        width: 25%;
    }

    .password-strength-fill.fair {
        background: var(--warning-gradient);
        width: 50%;
    }

    .password-strength-fill.good {
        background: var(--info-gradient);
        width: 75%;
    }

    .password-strength-fill.strong {
        background: var(--success-gradient);
        width: 100%;
    }

    .password-strength-text {
        font-size: 0.8rem;
        font-weight: 600;
        text-align: center;
        transition: var(--transition);
    }

    /* Styles pour le résumé de confirmation */
    .confirmation-summary {
        max-width: 600px;
        margin: 0 auto;
    }

    .summary-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-soft);
        margin-bottom: var(--spacing-lg);
        border: 1px solid var(--glass-border);
    }

    .summary-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .summary-header i {
        font-size: 1.5rem;
        color: #4f46e5;
    }

    .summary-header h5 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .user-summary {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: var(--spacing-lg);
        padding: var(--spacing-md);
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(99, 102, 241, 0.02));
        border-radius: var(--border-radius-small);
    }

    .summary-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--secondary-gradient);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }

    .summary-info h6 {
        margin: 0 0 0.25rem 0;
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .summary-info p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.95rem;
    }

    .summary-details {
        space-y: var(--spacing-sm);
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: var(--text-secondary);
        display: flex;
        align-items: center;
    }

    .detail-value {
        font-weight: 600;
        color: var(--text-primary);
    }

    .notice-card {
        background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(8, 145, 178, 0.05));
        border: 1px solid rgba(6, 182, 212, 0.2);
        border-radius: var(--border-radius-small);
        padding: var(--spacing-md);
        display: flex;
        gap: 1rem;
    }

    .notice-card i {
        color: #06b6d4;
        font-size: 1.25rem;
        margin-top: 0.25rem;
    }

    .notice-content h6 {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        font-weight: 700;
        color: var(--text-primary);
    }

    .notice-content ul {
        margin: 0;
        padding-left: 1.25rem;
        color: var(--text-secondary);
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .notice-content li {
        margin-bottom: 0.25rem;
    }

    /* Styles pour les badges */
    .badge {
        display: inline-block;
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 20px;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .badge.bg-primary {
        background: var(--secondary-gradient) !important;
        color: white;
    }

    .badge.bg-light {
        background: #f8fafc;
        color: #64748b;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;
    }

    .btn-secondary:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    /* Navigation des étapes */
    .step-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 1px solid #e5e7eb;
    }

    /* Indicateur de force du mot de passe */
    .password-strength-container {
        margin-top: 1rem;
    }

    .password-strength-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
    }

    .password-strength-bar {
        width: 100%;
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .password-strength-fill {
        height: 100%;
        width: 0%;
        transition: var(--transition);
        border-radius: 4px;
    }

    .password-strength-fill.weak {
        background: linear-gradient(90deg, #ef4444, #f87171);
        width: 25%;
    }

    .password-strength-fill.fair {
        background: linear-gradient(90deg, #f59e0b, #fbbf24);
        width: 50%;
    }

    .password-strength-fill.good {
        background: linear-gradient(90deg, #3b82f6, #60a5fa);
        width: 75%;
    }

    .password-strength-fill.strong {
        background: linear-gradient(90deg, #10b981, #34d399);
        width: 100%;
    }

    .password-strength-text {
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--text-muted);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .wizard-container {
            margin: 1rem;
            border-radius: 15px;
        }

        .step-content {
            padding: 2rem 1.5rem;
        }

        .step-progress {
            padding: 0 1rem;
        }

        .step-item {
            max-width: 120px;
        }

        .step-number {
            width: 50px;
            height: 50px;
            font-size: 1.1rem;
        }

        .step-title {
            font-size: 0.8rem;
        }

        .roles-grid {
            grid-template-columns: 1fr;
        }

        .step-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }

        .form-floating > label {
            left: 2.5rem;
        }

        .input-icon {
            left: 0.75rem;
        }
    }

    .form-step.prev {
        animation: slideOutLeft 0.5s ease-out;
    }

    .form-step.next {
        animation: slideInRight 0.5s ease-out;
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(50px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideOutLeft {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50px);
        }
    }

    /* Header moderne avec glassmorphism */
    .header-section {
        background: var(--glass-bg);
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--card-shadow);
    }

    .breadcrumb-modern {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1rem 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        font-size: 1.2em;
        color: #6c757d;
    }

    .breadcrumb-modern a {
        color: #495057;
        text-decoration: none;
        transition: var(--transition);
    }

    .breadcrumb-modern a:hover {
        color: #007bff;
        transform: translateX(2px);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .page-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .text-gradient {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Styles pour les étapes du formulaire */
    .form-card {
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .step-header {
        text-align: center;
        padding: 3rem 2rem 2rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .step-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .step-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #1a1a1a !important;
        margin-bottom: 0.5rem;
        text-shadow: 0 1px 3px rgba(255, 255, 255, 0.8);
    }

    .step-description {
        color: rgba(0, 0, 0, 0.7) !important;
        font-size: 1rem;
        margin: 0;
        line-height: 1.5;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6);
    }

    .step-content {
        padding: 3rem 2rem;
        min-height: 400px;
    }

    .step-navigation {
        padding: 2rem;
        background: rgba(255, 255, 255, 0.05);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Groupes de formulaire modernes */
    .form-group-modern {
        position: relative;
    }

    .input-group-modern {
        position: relative;
        display: flex;
        align-items: stretch;
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        color: rgba(0, 0, 0, 0.6) !important;
        font-size: 1.1rem;
        transition: var(--transition);
    }

    .form-control-modern {
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(15px);
        color: #1a1a1a !important;
        font-weight: 500;
        text-shadow: none;
    }

    .form-control-modern::placeholder {
        color: rgba(0, 0, 0, 0.6) !important;
    }

    .form-control-modern:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        background: rgba(255, 255, 255, 0.4);
        transform: translateY(-2px);
        color: #1a1a1a !important;
    }

    .form-floating > .form-control-modern:focus ~ label,
    .form-floating > .form-control-modern:not(:placeholder-shown) ~ label {
        color: #007bff !important;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        font-weight: 600;
    }

    .form-floating > label {
        color: rgba(0, 0, 0, 0.7) !important;
        padding-left: 3rem;
        font-weight: 500;
        text-shadow: none;
    }

    .form-hint {
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
    }

    /* Aperçu en temps réel */
    .live-preview-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: center;
        backdrop-filter: blur(10px);
    }

    .preview-header {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }

    .preview-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .preview-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--secondary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
        transition: var(--transition);
    }

    .preview-name {
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .preview-email {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.875rem;
        margin: 0;
    }

    /* Section sécurité */
    .security-tips {
        margin-bottom: 2rem;
    }

    .tip-card {
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: var(--border-radius);
        padding: 1rem;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .tip-card i {
        color: #ffc107;
        font-size: 1.5rem;
        margin-top: 0.25rem;
    }

    .tip-content strong {
        color: #ffc107;
        display: block;
        margin-bottom: 0.5rem;
    }

    .tip-content p {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    /* Champs de mot de passe améliorés */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        z-index: 10;
        transition: var(--transition);
        font-size: 1.1rem;
    }

    .password-toggle:hover {
        color: #007bff;
        transform: translateY(-50%) scale(1.1);
    }

    .password-strength-container {
        margin-top: 0.75rem;
    }

    .password-strength {
        height: 6px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .password-strength::after {
        content: '';
        display: block;
        height: 100%;
        border-radius: 3px;
        transition: var(--transition);
        width: 0%;
    }

    .password-strength.weak::after {
        width: 25%;
        background: linear-gradient(90deg, #dc3545, #ff6b7a);
    }

    .password-strength.medium::after {
        width: 60%;
        background: linear-gradient(90deg, #ffc107, #ffda6a);
    }

    .password-strength.strong::after {
        width: 100%;
        background: linear-gradient(90deg, #28a745, #5cb85c);
    }

    .strength-text {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.7);
        text-align: center;
    }

    .password-match {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .password-match.match {
        color: #28a745;
    }

    .password-match.no-match {
        color: #dc3545;
    }

    .password-requirements {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .password-requirements h6 {
        color: white;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .requirements-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .requirements-list li {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        transition: var(--transition);
    }

    .requirements-list li i {
        width: 16px;
        text-align: center;
        color: #dc3545;
        transition: var(--transition);
    }

    .requirements-list li.valid i {
        color: #28a745;
    }

    .requirements-list li.valid {
        color: rgba(255, 255, 255, 0.9);
    }

    /* Cards modernes */
    .animate-card {
        animation: slideInUp 0.6s ease-out;
        transition: var(--transition);
    }

    .animate-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Sélection de rôles moderne */
    .roles-selection {
        padding: 1rem 0;
    }

    .roles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .role-card-modern {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    .role-card-modern:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .role-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .role-label-modern {
        display: block;
        padding: 1.5rem;
        cursor: pointer;
        transition: var(--transition);
        margin: 0;
        height: 100%;
    }

    .role-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .role-icon-modern {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .role-level {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .level-critique {
        background: rgba(220, 53, 69, 0.2);
        color: #ff6b7a;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .level-élevé {
        background: rgba(0, 123, 255, 0.2);
        color: #4dabf7;
        border: 1px solid rgba(0, 123, 255, 0.3);
    }

    .level-moyen {
        background: rgba(255, 193, 7, 0.2);
        color: #ffd43b;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .level-faible {
        background: rgba(40, 167, 69, 0.2);
        color: #51cf66;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .level-personnalisé {
        background: rgba(108, 117, 125, 0.2);
        color: #adb5bd;
        border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .role-body {
        margin-bottom: 1.5rem;
    }

    .role-name-modern {
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        margin-bottom: 0.5rem;
    }

    .role-description-modern {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.4;
        margin: 0;
    }

    .role-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .role-check-modern {
        width: 28px;
        height: 28px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        color: transparent;
    }

    .selection-text {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 500;
    }

    .role-checkbox:checked + .role-label-modern {
        background: rgba(0, 123, 255, 0.1);
        border-color: #007bff;
    }

    .role-checkbox:checked + .role-label-modern .role-check-modern {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .role-checkbox:checked + .role-label-modern .selection-text {
        color: #007bff;
    }

    .role-checkbox:checked + .role-label-modern .role-name-modern {
        color: #007bff;
    }

    /* Aperçu des rôles sélectionnés */
    .selected-roles-preview {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .selected-roles-preview h6 {
        color: white;
        margin-bottom: 1rem;
        font-size: 1rem;
    }

    .selected-roles-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .selected-roles-container .badge {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        border-radius: 20px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .no-selection {
        color: rgba(255, 255, 255, 0.5);
        font-style: italic;
    }

    /* Section de confirmation */
    .confirmation-summary {
        padding: 1rem 0;
    }

    .summary-card {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
    }

    .summary-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .summary-header i {
        font-size: 1.5rem;
        color: #28a745;
    }

    .summary-header h5 {
        color: white;
        margin: 0;
        font-weight: 600;
    }

    .user-summary {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
    }

    .summary-avatar {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.8rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .summary-info h6 {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
    }

    .summary-info p {
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
    }

    .summary-details {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--border-radius);
    }

    .detail-label {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
    }

    .detail-value {
        color: white;
        font-weight: 600;
    }

    .summary-roles {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .creation-notice {
        margin-top: 1rem;
    }

    .notice-card {
        background: rgba(23, 162, 184, 0.1);
        border: 1px solid rgba(23, 162, 184, 0.3);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        display: flex;
        gap: 1rem;
    }

    .notice-card i {
        color: #17a2b8;
        font-size: 1.5rem;
        margin-top: 0.25rem;
    }

    .notice-content h6 {
        color: #17a2b8;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .notice-content ul {
        color: rgba(255, 255, 255, 0.8);
        margin: 0;
        padding-left: 1.5rem;
    }

    .notice-content li {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    /* Boutons modernes */
    .btn {
        border-radius: var(--border-radius);
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
        text-transform: none;
        letter-spacing: 0.5px;
    }

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .btn-outline-secondary {
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);
        background: transparent;
        backdrop-filter: blur(10px);
    }

    .btn-outline-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border-color: rgba(255, 255, 255, 0.5);
    }

    .btn-outline-light {
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
    }

    /* Bouton flottant de retour */
    .floating-back-btn {
        position: fixed;
        top: 2rem;
        left: 2rem;
        z-index: 1000;
    }

    .btn-floating {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
    }

    /* Couleurs spécifiques */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .container-fluid {
            margin: 1rem;
            padding: 1rem;
        }

        .page-title {
            font-size: 2rem;
        }

        .creation-stepper {
            flex-direction: column;
            gap: 1rem;
        }

        .step {
            max-width: none;
        }

        .step-connector {
            display: none;
        }

        .step-circle {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
        }

        .step-content {
            padding: 2rem 1rem;
            min-height: auto;
        }

        .roles-grid {
            grid-template-columns: 1fr;
        }

        .step-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .btn {
            width: 100%;
        }

        .floating-back-btn {
            top: 1rem;
            left: 1rem;
        }

        .user-summary {
            flex-direction: column;
            text-align: center;
        }

        .detail-row {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }
    }

    @media (max-width: 576px) {
        .step-header {
            padding: 2rem 1rem 1rem;
        }

        .step-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .step-title {
            font-size: 1.5rem;
        }

        .form-control-modern {
            padding: 0.875rem 0.875rem 0.875rem 2.5rem;
        }

        .input-icon {
            left: 0.75rem;
            font-size: 1rem;
        }

        .form-floating > label {
            padding-left: 2.5rem;
        }
    }

    /* Animations d'entrée */
    .animate-card {
        animation: slideInUp 0.8s ease-out;
    }

    .form-step {
        animation: fadeInScale 0.6s ease-out;
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.95);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* États de validation */
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    .invalid-feedback {
        color: #ff6b7a;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .invalid-feedback::before {
        content: '⚠';
        font-size: 1rem;
    }

    /* Effets de focus améliorés */
    .form-control-modern:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1), 0 4px 15px rgba(0, 123, 255, 0.2);
    }

    /* Transitions fluides pour les étapes */
    .form-step {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-step:not(.active) {
        pointer-events: none;
    }

    /* Amélioration de l'accessibilité */
    .btn:focus,
    .form-control-modern:focus,
    .role-label-modern:focus {
        outline: 2px solid #007bff;
        outline-offset: 2px;
    }

    /* Indicateurs de progression */
    .step.active .step-circle {
        animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
        0%, 100% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        50% {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6), 0 0 20px rgba(102, 126, 234, 0.3);
        }
    }

    /* Champs de mot de passe */
    .password-field {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        transition: var(--transition);
    }

    .password-toggle:hover {
        color: #007bff;
    }

    .password-strength {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        margin-top: 0.5rem;
        overflow: hidden;
    }

    .password-strength::after {
        content: '';
        display: block;
        height: 100%;
        border-radius: 2px;
        transition: var(--transition);
    }

    .password-strength.weak::after {
        width: 33%;
        background: #dc3545;
    }

    .password-strength.medium::after {
        width: 66%;
        background: #ffc107;
    }

    .password-strength.strong::after {
        width: 100%;
        background: #28a745;
    }

    .password-match {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .password-match.match {
        color: #28a745;
    }

    .password-match.no-match {
        color: #dc3545;
    }

    .password-requirements {
        margin-top: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        border-left: 4px solid #007bff;
    }

    /* Cartes de rôles */
    .roles-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .role-card {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: var(--transition);
        background: #fff;
        border: 2px solid #e9ecef;
    }

    .role-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
    }

    .role-checkbox {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .role-label {
        display: flex;
        align-items: center;
        padding: 1rem;
        cursor: pointer;
        transition: var(--transition);
        margin: 0;
    }

    .role-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .role-info {
        flex: 1;
    }

    .role-name {
        font-weight: 600;
        font-size: 1rem;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .role-description {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.4;
    }

    .role-check {
        width: 24px;
        height: 24px;
        border: 2px solid #dee2e6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition);
        color: transparent;
        flex-shrink: 0;
    }

    .role-checkbox:checked + .role-label {
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        border-color: #007bff;
    }

    .role-checkbox:checked + .role-label .role-check {
        background: #007bff;
        border-color: #007bff;
        color: white;
        transform: scale(1.1);
    }

    .role-checkbox:checked + .role-label .role-name {
        color: #007bff;
    }

    /* Couleurs spécifiques pour les rôles */
    .bg-purple {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Boutons modernes */
    .btn-modern {
        border-radius: var(--border-radius);
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary.btn-modern {
        background: var(--primary-gradient);
    }

    .btn-outline-secondary.btn-modern {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: transparent;
    }

    .btn-outline-secondary.btn-modern:hover {
        background: #6c757d;
        color: white;
    }

    /* Actions du formulaire */
    .form-actions {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e9ecef;
    }

    /* Panneau d'aide */
    .help-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
    }

    .help-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .help-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.5;
        margin: 0;
    }

    /* Aperçu utilisateur */
    .user-preview {
        padding: 1rem 0;
    }

    .preview-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin: 0 auto 1rem;
        transition: var(--transition);
    }

    .preview-avatar:hover {
        transform: scale(1.1);
    }

    .preview-name {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #212529;
    }

    .preview-email {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .preview-roles .badge {
        margin: 0.25rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 20px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }

        .roles-container {
            grid-template-columns: 1fr;
        }

        .form-actions .d-flex {
            flex-direction: column;
            gap: 1rem;
        }

        .btn-modern {
            width: 100%;
        }
    }

    /* Animations d'entrée */
    .form-section {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .form-section:nth-child(1) { animation-delay: 0.1s; }
    .form-section:nth-child(2) { animation-delay: 0.2s; }
    .form-section:nth-child(3) { animation-delay: 0.3s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endsection

@section('content')
<!-- Bouton de retour flottant -->
<a href="{{ route('admin.users.index') }}" class="floating-back-btn">
    <i class="fas fa-arrow-left"></i>
    <span>Retour</span>
</a>

<div class="wizard-container">
    <!-- En-tête du wizard -->
    <div class="wizard-header">
        <div class="wizard-icon">
            <i class="fas fa-user-plus"></i>
        </div>
        <h1 class="wizard-title">Créer un utilisateur</h1>
        <p class="wizard-subtitle">Suivez les étapes pour ajouter un nouveau membre à votre équipe</p>

        <!-- Indicateur de progression -->
        <div class="progress-indicator">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text">
                <span id="currentStep">1</span> sur <span id="totalSteps">4</span> étapes
            </div>
        </div>
    </div>

    <!-- Indicateur de progression -->
    <div class="step-progress">
        <div class="step-item active" data-step="1">
            <div class="step-number">1</div>
            <div class="step-title">Informations<br>personnelles</div>
            <div class="step-connector"></div>
        </div>
        <div class="step-item" data-step="2">
            <div class="step-number">2</div>
            <div class="step-title">Sécurité</div>
            <div class="step-connector"></div>
        </div>
        <div class="step-item" data-step="3">
            <div class="step-number">3</div>
            <div class="step-title">Rôles</div>
            <div class="step-connector"></div>
        </div>
        <div class="step-item" data-step="4">
            <div class="step-number">4</div>
            <div class="step-title">Confirmation</div>
        </div>
    </div>

    <!-- Contenu du formulaire -->
    <form method="POST" action="{{ route('admin.users.store') }}" id="userForm">
        @csrf

        <!-- Étape 1: Informations personnelles -->
        <div class="step-content active" id="step-1">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-user"></i>
                </div>
                <h2 class="step-title">Informations personnelles</h2>
                <p class="step-description">Saisissez les informations de base de l'utilisateur</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text"
                               class="form-control-modern @error('name') is-invalid @enderror"
                               id="name"
                               name="name"
                               value="{{ old('name') }}"
                               placeholder="Nom complet"
                               required>
                        <label for="name">Nom complet *</label>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating">
                        <i class="fas fa-envelope input-icon"></i>
                        <input type="email"
                               class="form-control-modern @error('email') is-invalid @enderror"
                               id="email"
                               name="email"
                               value="{{ old('email') }}"
                               placeholder="Adresse email"
                               required>
                        <label for="email">Adresse email *</label>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" disabled>
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>
        </div>

        <!-- Étape 2: Sécurité -->
        <div class="step-content" id="step-2">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2 class="step-title">Sécurité et accès</h2>
                <p class="step-description">Définissez les paramètres de sécurité du compte</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password"
                               class="form-control-modern @error('password') is-invalid @enderror"
                               id="password"
                               name="password"
                               placeholder="Mot de passe"
                               required>
                        <label for="password">Mot de passe *</label>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password"
                               class="form-control-modern"
                               id="password_confirmation"
                               name="password_confirmation"
                               placeholder="Confirmer le mot de passe"
                               required>
                        <label for="password_confirmation">Confirmer le mot de passe *</label>
                    </div>
                </div>
            </div>

            <!-- Indicateur de force du mot de passe -->
            <div class="password-strength-container" style="margin-top: 1rem;">
                <div class="password-strength-label">Force du mot de passe:</div>
                <div class="password-strength-bar">
                    <div class="password-strength-fill" id="passwordStrength"></div>
                </div>
                <div class="password-strength-text" id="passwordStrengthText">Saisissez un mot de passe</div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" onclick="prevStep(1)">
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>
        </div>

        <!-- Étape 3: Rôles -->
        <div class="step-content" id="step-3">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-user-tag"></i>
                </div>
                <h2 class="step-title">Rôles et permissions</h2>
                <p class="step-description">Sélectionnez les rôles à attribuer à l'utilisateur</p>
            </div>

            <div class="roles-grid">
                @foreach($roles as $role)
                <div class="role-card" data-role="{{ $role->name }}">
                    <div class="role-header">
                        <div class="role-icon" style="background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div>
                            <h5 class="role-name">{{ $role->name }}</h5>
                            <p class="role-description">{{ $role->description ?? 'Rôle ' . $role->name }}</p>
                        </div>
                    </div>
                    <input type="checkbox" name="roles[]" value="{{ $role->name }}" class="d-none role-checkbox">
                </div>
                @endforeach
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" onclick="prevStep(2)">
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                    Suivant<i class="fas fa-arrow-right ms-2"></i>
                </button>
            </div>
        </div>

        <!-- Étape 4: Confirmation et création -->
        <div class="step-content" id="step-4">
            <div class="step-header">
                <div class="step-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="step-title">Confirmation et création</h3>
                <p class="step-description">Vérifiez les informations avant de créer l'utilisateur</p>
            </div>

            <div class="confirmation-summary">
                <div class="summary-card">
                    <div class="summary-header">
                        <i class="fas fa-user-check"></i>
                        <h5>Récapitulatif de l'utilisateur</h5>
                    </div>

                    <div class="summary-content">
                        <div class="user-summary">
                            <div class="summary-avatar" id="summary-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="summary-info">
                                <h6 class="summary-name" id="summary-name">Nom de l'utilisateur</h6>
                                <p class="summary-email" id="summary-email"><EMAIL></p>
                            </div>
                        </div>

                        <div class="summary-details">
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="fas fa-shield-alt me-2"></i>Mot de passe
                                </span>
                                <span class="detail-value">
                                    <i class="fas fa-check-circle text-success"></i> Configuré
                                </span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="fas fa-user-tag me-2"></i>Rôles assignés
                                </span>
                                <div class="detail-value">
                                    <div class="summary-roles" id="summary-roles">
                                        <span class="badge bg-light text-muted">Aucun rôle</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="creation-notice">
                    <div class="notice-card">
                        <i class="fas fa-info-circle"></i>
                        <div class="notice-content">
                            <h6>Informations importantes</h6>
                            <ul>
                                <li>L'utilisateur recevra un email de bienvenue</li>
                                <li>Il pourra modifier son mot de passe lors de sa première connexion</li>
                                <li>Les permissions seront appliquées immédiatement</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="step-navigation">
                <button type="button" class="btn btn-secondary" onclick="prevStep(3)">
                    <i class="fas fa-arrow-left me-2"></i>Précédent
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-user-plus me-2"></i>Créer l'utilisateur
                </button>
            </div>
        </div>

    </form>
</div>
</div>
@endsection

@section('scripts')
<script>
    let currentStep = 1;
    const totalSteps = 4;
    let formData = {};

    // Navigation entre les étapes avec animations améliorées
    function nextStep(step) {
        if (validateCurrentStep()) {
            saveCurrentStepData();
            showStep(step);
            addStepCompletionEffect();
        } else {
            showValidationErrors();
        }
    }

    function prevStep(step) {
        showStep(step);
    }

    function showStep(step) {
        // Animation de sortie pour l'étape actuelle
        const currentStepElement = document.querySelector('.step-content.active');
        if (currentStepElement) {
            currentStepElement.style.animation = 'slideOutLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            setTimeout(() => {
                currentStepElement.classList.remove('active');
            }, 400);
        }

        // Mise à jour des indicateurs d'étapes avec animations
        document.querySelectorAll('.step-item').forEach((el, index) => {
            el.classList.remove('active', 'completed');

            if (index + 1 < step) {
                setTimeout(() => el.classList.add('completed'), 200 + index * 100);
            } else if (index + 1 === step) {
                setTimeout(() => el.classList.add('active'), 200 + index * 100);
            }
        });

        // Animation d'entrée pour la nouvelle étape
        setTimeout(() => {
            const targetStep = document.getElementById(`step-${step}`);
            if (targetStep) {
                targetStep.classList.add('active');
                targetStep.style.animation = 'slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1)';

                // Animation des éléments internes
                const elements = targetStep.querySelectorAll('.form-floating, .role-card, .btn');
                elements.forEach((el, index) => {
                    el.style.opacity = '0';
                    el.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        el.style.transition = 'all 0.3s ease-out';
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, 100 + index * 50);
                });
            }
        }, 400);

        currentStep = step;
        updateProgress(step);

        // Actions spécifiques par étape
        if (step === 4) {
            updateSummary();
            generateUserAvatar();
        }
    }

    // Sauvegarde des données de l'étape actuelle
    function saveCurrentStepData() {
        const currentStepElement = document.getElementById(`step-${currentStep}`);
        const inputs = currentStepElement.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                formData[input.name] = input.checked;
            } else {
                formData[input.name] = input.value;
            }
        });
    }

    // Effet visuel de complétion d'étape
    function addStepCompletionEffect() {
        const completedStep = document.querySelector(`[data-step="${currentStep}"]`);
        if (completedStep) {
            // Effet de particules
            createParticleEffect(completedStep);

            // Son de succès (optionnel)
            playSuccessSound();
        }
    }

    // Création d'effet de particules
    function createParticleEffect(element) {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < 8; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 6px;
                height: 6px;
                background: linear-gradient(45deg, #10b981, #34d399);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                left: ${centerX}px;
                top: ${centerY}px;
            `;

            document.body.appendChild(particle);

            const angle = (i / 8) * Math.PI * 2;
            const distance = 50 + Math.random() * 30;
            const duration = 800 + Math.random() * 400;

            particle.animate([
                { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                {
                    transform: `translate(${Math.cos(angle) * distance}px, ${Math.sin(angle) * distance}px) scale(0)`,
                    opacity: 0
                }
            ], {
                duration: duration,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }).onfinish = () => particle.remove();
        }
    }

    // Son de succès (optionnel)
    function playSuccessSound() {
        // Vous pouvez ajouter un son ici si souhaité
        // const audio = new Audio('/sounds/success.mp3');
        // audio.volume = 0.3;
        // audio.play().catch(() => {});
    }

    // Mise à jour de la progression avec animations
    function updateProgress(step) {
        const progressFill = document.getElementById('progressFill');
        const currentStepElement = document.getElementById('currentStep');

        if (progressFill && currentStepElement) {
            const progress = (step / totalSteps) * 100;

            // Animation fluide de la barre de progression
            progressFill.style.transition = 'width 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            progressFill.style.width = `${progress}%`;

            // Animation du texte
            currentStepElement.style.transform = 'scale(1.2)';
            setTimeout(() => {
                currentStepElement.textContent = step;
                currentStepElement.style.transform = 'scale(1)';
            }, 200);
        }
    }

    // Validation avancée des étapes
    function validateCurrentStep() {
        const currentStepElement = document.getElementById(`step-${currentStep}`);
        const requiredFields = currentStepElement.querySelectorAll('input[required], select[required]');
        let isValid = true;
        let errors = [];

        // Validation des champs requis
        requiredFields.forEach(field => {
            const value = field.value.trim();
            field.classList.remove('is-invalid');

            if (!value) {
                field.classList.add('is-invalid');
                errors.push(`Le champ "${field.placeholder || field.name}" est requis`);
                isValid = false;

                // Animation de secousse
                field.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => field.style.animation = '', 500);
            }
        });

        // Validations spécifiques par étape
        switch (currentStep) {
            case 1:
                isValid = validateStep1() && isValid;
                break;
            case 2:
                isValid = validateStep2() && isValid;
                break;
            case 3:
                isValid = validateStep3() && isValid;
                break;
        }

        return isValid;
    }

    // Validation étape 1 - Informations personnelles
    function validateStep1() {
        let isValid = true;
        const nameField = document.getElementById('name');
        const emailField = document.getElementById('email');

        // Validation du nom
        if (nameField.value.length < 2) {
            nameField.classList.add('is-invalid');
            showFieldError(nameField, 'Le nom doit contenir au moins 2 caractères');
            isValid = false;
        }

        // Validation de l'email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailField.value)) {
            emailField.classList.add('is-invalid');
            showFieldError(emailField, 'Veuillez saisir une adresse email valide');
            isValid = false;
        }

        return isValid;
    }

    // Validation étape 2 - Sécurité
    function validateStep2() {
        let isValid = true;
        const password = document.getElementById('password');
        const passwordConfirm = document.getElementById('password_confirmation');

        // Validation de la force du mot de passe
        const strength = calculatePasswordStrength(password.value);
        if (strength < 3) {
            password.classList.add('is-invalid');
            showFieldError(password, 'Le mot de passe doit être plus fort');
            isValid = false;
        }

        // Validation de la confirmation
        if (password.value !== passwordConfirm.value) {
            passwordConfirm.classList.add('is-invalid');
            showFieldError(passwordConfirm, 'Les mots de passe ne correspondent pas');
            isValid = false;
        }

        return isValid;
    }

    // Validation étape 3 - Rôles
    function validateStep3() {
        const selectedRoles = document.querySelectorAll('.role-card.selected');
        if (selectedRoles.length === 0) {
            showNotification('Veuillez sélectionner au moins un rôle', 'warning');
            return false;
        }
        return true;
    }

    // Affichage des erreurs de validation
    function showValidationErrors() {
        // Animation de secousse pour le conteneur
        const currentStepElement = document.getElementById(`step-${currentStep}`);
        currentStepElement.style.animation = 'shake 0.6s ease-in-out';
        setTimeout(() => currentStepElement.style.animation = '', 600);

        // Notification d'erreur
        showNotification('Veuillez corriger les erreurs avant de continuer', 'error');
    }

    // Affichage d'erreur pour un champ spécifique
    function showFieldError(field, message) {
        // Supprimer l'ancien message d'erreur
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) existingError.remove();

        // Créer le nouveau message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = `
            color: #ef4444;
            font-size: 0.8rem;
            margin-top: 0.25rem;
            animation: fadeIn 0.3s ease-out;
        `;
        errorDiv.textContent = message;

        field.parentNode.appendChild(errorDiv);

        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => errorDiv.remove(), 300);
            }
        }, 5000);
    }

    // Mise à jour du résumé avec animations
    function updateSummary() {
        const nameElement = document.getElementById('summary-name');
        const emailElement = document.getElementById('summary-email');
        const rolesElement = document.getElementById('summary-roles');

        // Animation de mise à jour
        [nameElement, emailElement, rolesElement].forEach(el => {
            el.style.opacity = '0.5';
            el.style.transform = 'translateY(10px)';
        });

        setTimeout(() => {
            nameElement.textContent = document.getElementById('name').value || 'Non renseigné';
            emailElement.textContent = document.getElementById('email').value || 'Non renseigné';

            // Mise à jour des rôles
            const selectedRoles = Array.from(document.querySelectorAll('.role-card.selected'))
                .map(card => card.dataset.role);

            if (selectedRoles.length > 0) {
                rolesElement.innerHTML = selectedRoles.map(role =>
                    `<span class="badge bg-primary me-1">${role}</span>`
                ).join('');
            } else {
                rolesElement.innerHTML = '<span class="badge bg-light text-muted">Aucun rôle sélectionné</span>';
            }

            // Animation de retour
            [nameElement, emailElement, rolesElement].forEach((el, index) => {
                setTimeout(() => {
                    el.style.transition = 'all 0.3s ease-out';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }, 200);
    }

    // Génération d'avatar utilisateur
    function generateUserAvatar() {
        const avatarElement = document.getElementById('summary-avatar');
        const userName = document.getElementById('name').value;

        if (userName && avatarElement) {
            const initials = userName.split(' ')
                .map(word => word.charAt(0).toUpperCase())
                .slice(0, 2)
                .join('');

            avatarElement.innerHTML = initials;
            avatarElement.style.background = `linear-gradient(135deg,
                hsl(${userName.charCodeAt(0) * 7 % 360}, 70%, 60%),
                hsl(${userName.charCodeAt(0) * 7 % 360 + 30}, 70%, 70%))`;
        }
    }

    // Calcul de la force du mot de passe
    function calculatePasswordStrength(password) {
        let strength = 0;

        if (password.length >= 8) strength++;
        if (password.length >= 12) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        return Math.min(strength, 5);
    }

    // Mise à jour de l'indicateur de force du mot de passe
    function updatePasswordStrength(password) {
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');

        if (!strengthBar || !strengthText) return;

        const strength = calculatePasswordStrength(password);

        // Réinitialiser les classes
        strengthBar.className = 'password-strength-fill';

        let feedback = '';
        let className = '';

        switch (strength) {
            case 0:
            case 1:
                className = 'weak';
                feedback = 'Très faible';
                break;
            case 2:
                className = 'weak';
                feedback = 'Faible';
                break;
            case 3:
                className = 'fair';
                feedback = 'Moyen';
                break;
            case 4:
                className = 'good';
                feedback = 'Bon';
                break;
            case 5:
                className = 'strong';
                feedback = 'Très fort';
                break;
        }

        strengthBar.classList.add(className);
        strengthText.textContent = feedback;

        // Animation de pulsation pour les mots de passe forts
        if (strength >= 4) {
            strengthBar.style.animation = 'pulse 1s ease-in-out';
            setTimeout(() => strengthBar.style.animation = '', 1000);
        }
    }

    // Gestion des cartes de rôles
    function initializeRoleCards() {
        document.querySelectorAll('.role-card').forEach(card => {
            card.addEventListener('click', function() {
                const checkbox = this.querySelector('.role-checkbox');
                const isSelected = this.classList.contains('selected');

                if (isSelected) {
                    this.classList.remove('selected');
                    checkbox.checked = false;

                    // Animation de désélection
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => this.style.transform = '', 200);
                } else {
                    this.classList.add('selected');
                    checkbox.checked = true;

                    // Animation de sélection
                    this.style.transform = 'scale(1.05)';
                    setTimeout(() => this.style.transform = '', 200);

                    // Effet de particules
                    createSelectionEffect(this);
                }

                // Mise à jour du résumé en temps réel si on est à l'étape 4
                if (currentStep === 4) {
                    updateSummary();
                }
            });

            // Effet de survol amélioré
            card.addEventListener('mouseenter', function() {
                if (!this.classList.contains('selected')) {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                }
            });

            card.addEventListener('mouseleave', function() {
                if (!this.classList.contains('selected')) {
                    this.style.transform = '';
                }
            });
        });
    }

    // Effet visuel de sélection
    function createSelectionEffect(element) {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(79, 70, 229, 0.3), transparent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            left: ${centerX - 10}px;
            top: ${centerY - 10}px;
        `;

        document.body.appendChild(ripple);

        ripple.animate([
            { transform: 'scale(1)', opacity: 0.7 },
            { transform: 'scale(8)', opacity: 0 }
        ], {
            duration: 600,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }).onfinish = () => ripple.remove();
    }

        const selectedRoles = Array.from(document.querySelectorAll('input[name="roles[]"]:checked'))
            .map(checkbox => checkbox.value);
        document.getElementById('summary-roles').textContent = selectedRoles.length > 0
            ? selectedRoles.join(', ')
            : 'Aucun rôle sélectionné';
    }

    // Fonction pour évaluer la force du mot de passe
    function checkPasswordStrength(password) {
        let strength = 0;
        let feedback = '';

        if (password.length >= 8) strength += 1;
        if (password.match(/[a-z]/)) strength += 1;
        if (password.match(/[A-Z]/)) strength += 1;
        if (password.match(/[0-9]/)) strength += 1;
        if (password.match(/[^a-zA-Z0-9]/)) strength += 1;

        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');

        if (!strengthBar || !strengthText) return;

        strengthBar.className = 'password-strength-fill';

        switch (strength) {
            case 0:
            case 1:
                strengthBar.classList.add('weak');
                feedback = 'Très faible';
                break;
            case 2:
                strengthBar.classList.add('weak');
                feedback = 'Faible';
                break;
            case 3:
                strengthBar.classList.add('fair');
                feedback = 'Moyen';
                break;
            case 4:
                strengthBar.classList.add('good');
                feedback = 'Bon';
                break;
            case 5:
                strengthBar.classList.add('strong');
                feedback = 'Très fort';
                break;
        }

        strengthText.textContent = feedback;
    }

    // Gestion des cartes de rôles et autres interactions
    document.addEventListener('DOMContentLoaded', function() {
        // Indicateur de force du mot de passe
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value);
            });
        }

        // Sélection des rôles avec animations
        document.querySelectorAll('.role-card').forEach(card => {
            card.addEventListener('click', function() {
                const checkbox = this.querySelector('.role-checkbox');
                checkbox.checked = !checkbox.checked;
                this.classList.toggle('selected', checkbox.checked);

                // Animation de sélection
                if (checkbox.checked) {
                    this.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                }
            });
        });

        // Validation en temps réel avec animations
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('is-invalid');
                    this.style.animation = 'shake 0.5s ease-in-out';
                } else {
                    this.classList.remove('is-invalid');
                    this.style.animation = '';
                }
            });

            // Animation de focus
            input.addEventListener('focus', function() {
                this.style.animation = '';
            });
        });

        // Système de notifications
        window.showNotification = function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 12px;
                color: white;
                font-weight: 600;
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease-out;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                max-width: 300px;
            `;

            // Couleurs selon le type
            const colors = {
                success: 'linear-gradient(135deg, #10b981, #059669)',
                error: 'linear-gradient(135deg, #ef4444, #dc2626)',
                warning: 'linear-gradient(135deg, #f59e0b, #d97706)',
                info: 'linear-gradient(135deg, #06b6d4, #0891b2)'
            };

            notification.style.background = colors[type] || colors.info;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animation d'entrée
            setTimeout(() => notification.style.transform = 'translateX(0)', 100);

            // Suppression automatique
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        };

        // Animation de secousse pour les erreurs
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            @keyframes slideOutLeft {
                from { opacity: 1; transform: translateX(0); }
                to { opacity: 0; transform: translateX(-100%); }
            }
            @keyframes slideInRight {
                from { opacity: 0; transform: translateX(100%); }
                to { opacity: 1; transform: translateX(0); }
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Message de bienvenue
        setTimeout(() => {
            showNotification('Bienvenue dans l\'assistant de création d\'utilisateur !', 'info');
        }, 1000);
    });
</script>
@endsection
