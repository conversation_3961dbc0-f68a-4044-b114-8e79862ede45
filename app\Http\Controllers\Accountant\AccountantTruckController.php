<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Truck;
use App\Models\TruckCapacity;
use App\Models\Driver;
use App\Traits\TruckManagement;
use Illuminate\Http\Request;

class AccountantTruckController extends Controller
{
    use TruckManagement;

    public function index()
    {
        $trucks = Truck::select([
            'trucks.*',
            'truck_capacities.name as capacity_name',
            'truck_capacities.capacity as capacity_value',
            'truck_capacities.unit as capacity_unit'
        ])
        ->leftJoin('truck_capacities', 'trucks.truck_capacity_id', '=', 'truck_capacities.id')
        ->with('driver')
        ->orderBy('trucks.registration_number')
        ->paginate(10);

        return view('accountant.trucks.index', compact('trucks'));
    }

    public function create()
    {
        $capacities = TruckCapacity::orderBy('capacity')->get();
        return view('accountant.trucks.create', compact('capacities'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'registration_number' => 'required|string|unique:trucks',
            'brand' => 'required|string',
            'model' => 'required|string',
            'truck_capacity_id' => 'required|exists:truck_capacities,id',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'status' => 'required|in:available,maintenance,busy',
            'notes' => 'nullable|string'
        ]);

        $truck = Truck::create($validated);

        return redirect()
            ->route('accountant.trucks.index')
            ->with('success', 'Véhicule ajouté avec succès');
    }

    public function edit(Truck $truck)
    {
        $capacities = TruckCapacity::orderBy('capacity')->get();
        return view('accountant.trucks.edit', compact('truck', 'capacities'));
    }

    public function update(Request $request, Truck $truck)
    {
        $validated = $request->validate([
            'registration_number' => 'required|string|unique:trucks,registration_number,' . $truck->id,
            'brand' => 'required|string',
            'model' => 'required|string',
            'truck_capacity_id' => 'required|exists:truck_capacities,id',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'status' => 'required|in:available,maintenance,busy',
            'notes' => 'nullable|string'
        ]);

        $truck->update($validated);

        return redirect()
            ->route('accountant.trucks.index')
            ->with('success', 'Véhicule mis à jour avec succès');
    }

    public function destroy(Truck $truck)
    {
        $truck->delete();

        return redirect()
            ->route('accountant.trucks.index')
            ->with('success', 'Véhicule supprimé avec succès');
    }

    public function show(Truck $truck)
    {
        $truck->load(['capacity', 'driver']);
        return view('accountant.trucks.show', compact('truck'));
    }

    public function getAvailableTrucks()
    {
        try {
            $trucks = Truck::select([
                'trucks.id',
                'trucks.registration_number',
                'trucks.status',
                'truck_capacities.capacity as capacity_value',
                'truck_capacities.unit as capacity_unit'
            ])
            ->leftJoin('truck_capacities', 'trucks.truck_capacity_id', '=', 'truck_capacities.id')
            ->with('driver')
            ->whereHas('driver')
            ->where(function($query) {
                $query->where('trucks.status', 'available')
                      ->orWhere('trucks.status', 'assigned');
            })
            ->get();

            return response()->json([
                'success' => true,
                'trucks' => $trucks
            ]);
        } catch (\Exception $e) {
            \Log::error('Erreur lors de la récupération des camions', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la récupération des camions'
            ], 500);
        }
    }
}
