<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class SettingController extends Controller
{
    public function index()
    {
        $settings = [
            'app_name' => config('app.name'),
            'app_email' => config('mail.from.address'),
            'app_currency' => config('app.currency', 'FCFA'),
            'tax_rate' => config('app.tax_rate', 18),
            'backup_enabled' => config('app.backup_enabled', true),
        ];

        return view('admin.settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'app_name' => 'required|string|max:255',
            'app_email' => 'required|email',
            'app_currency' => 'required|string|max:10',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'backup_enabled' => 'boolean'
        ]);

        // Mettre à jour les paramètres dans la base de données ou le fichier .env
        foreach ($validated as $key => $value) {
            $this->updateSetting($key, $value);
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Paramètres mis à jour avec succès.');
    }

    public function backup()
    {
        try {
            // Créer une sauvegarde de la base de données
            $filename = 'backup-' . date('Y-m-d-H-i-s') . '.sql';
            $command = sprintf(
                'mysqldump -u%s -p%s %s > %s',
                config('database.connections.mysql.username'),
                config('database.connections.mysql.password'),
                config('database.connections.mysql.database'),
                storage_path('app/backups/' . $filename)
            );

            exec($command);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Sauvegarde créée avec succès.');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                ->with('error', 'Erreur lors de la création de la sauvegarde : ' . $e->getMessage());
        }
    }

    public function restore(Request $request)
    {
        $request->validate([
            'backup_file' => 'required|file|mimes:sql'
        ]);

        try {
            $file = $request->file('backup_file');
            $path = $file->storeAs('temp', $file->getClientOriginalName());

            // Restaurer la base de données
            $command = sprintf(
                'mysql -u%s -p%s %s < %s',
                config('database.connections.mysql.username'),
                config('database.connections.mysql.password'),
                config('database.connections.mysql.database'),
                storage_path('app/' . $path)
            );

            exec($command);
            Storage::delete($path);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Base de données restaurée avec succès.');
        } catch (\Exception $e) {
            return redirect()->route('admin.settings.index')
                ->with('error', 'Erreur lors de la restauration : ' . $e->getMessage());
        }
    }

    private function updateSetting($key, $value)
    {
        // Mettre à jour dans la base de données
        DB::table('settings')->updateOrInsert(
            ['key' => $key],
            [
                'value' => $value,
                'group' => 'general', 
                'updated_at' => now()
            ]
        );

        // Mettre à jour le cache si nécessaire
        cache()->forget('setting.' . $key);
    }
}
