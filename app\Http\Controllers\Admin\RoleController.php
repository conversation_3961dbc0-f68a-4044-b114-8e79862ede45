<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Str;

class RoleController extends Controller
{
    public function index()
    {
        $roles = Role::with(['permissions', 'users'])
            ->withCount('users')
            ->get()
            ->map(function ($role) {
                $role->last_used = Cache::get("role.{$role->id}.last_used");
                $role->activity_level = $this->calculateActivityLevel($role);
                $role->security_score = $this->calculateSecurityScore($role);
                $role->security_color = $this->getSecurityScoreColor($role->security_score);
                return $role;
            });

        $permissionStats = Permission::withCount('roles')
            ->get()
            ->groupBy(function ($permission) {
                return explode('.', $permission->name)[0];
            });

        $roleStats = [
            'total_roles' => $roles->count(),
            'total_users' => $roles->sum('users_count'),
            'most_used' => $roles->sortByDesc('users_count')->first(),
            'least_used' => $roles->sortBy('users_count')->first(),
            'permission_distribution' => $roles->map(function ($role) {
                return [
                    'name' => $role->name,
                    'permissions_count' => $role->permissions->count()
                ];
            })
        ];

        return view('admin.roles.index', compact('roles', 'permissionStats', 'roleStats'));
    }

    public function create()
    {
        $permissions = Permission::all();
        $permissionGroups = $permissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0];
        });

        $recommendedTemplates = $this->getRecommendedTemplates();
        return view('admin.roles.create', compact('permissionGroups', 'recommendedTemplates'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
            'description' => 'nullable|string|max:500',
            'color' => 'required|string|max:7',
            'priority_level' => 'required|integer|min:1|max:10'
        ]);

        $role = Role::create([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'color' => $validated['color'],
            'priority_level' => $validated['priority_level'],
            'created_by' => auth()->id()
        ]);

        $role->syncPermissions($validated['permissions']);

        // Créer des règles de sécurité automatiques
        $this->generateSecurityRules($role);

        return redirect()->route('admin.roles.index')
            ->with('success', 'Rôle créé avec succès.');
    }

    public function edit(Role $role)
    {
        $permissions = Permission::all();
        $permissionGroups = $permissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0];
        });

        $roleHistory = $this->getRoleHistory($role);
        $securityAnalysis = $this->analyzeRoleSecurity($role);
        $userStats = $this->getUserStats($role);

        return view('admin.roles.edit', compact(
            'role', 
            'permissionGroups', 
            'roleHistory',
            'securityAnalysis',
            'userStats'
        ));
    }

    public function update(Request $request, Role $role)
    {
        if ($role->name === 'admin' && !auth()->user()->hasRole('admin')) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Vous n\'avez pas les permissions nécessaires pour modifier le rôle administrateur.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
            'description' => 'nullable|string|max:500',
            'color' => 'required|string|max:7',
            'priority_level' => 'required|integer|min:1|max:10'
        ]);

        // Sauvegarder l'historique des modifications
        $this->logRoleChanges($role, $validated);

        $role->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'color' => $validated['color'],
            'priority_level' => $validated['priority_level'],
            'updated_by' => auth()->id()
        ]);

        $role->syncPermissions($validated['permissions']);

        // Mettre à jour les règles de sécurité
        $this->updateSecurityRules($role);

        // Notifier les utilisateurs affectés
        $this->notifyAffectedUsers($role);

        return redirect()->route('admin.roles.index')
            ->with('success', 'Rôle mis à jour avec succès.');
    }

    public function destroy(Role $role)
    {
        if ($role->name === 'admin') {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Le rôle administrateur ne peut pas être supprimé.');
        }

        // Sauvegarder les données pour l'audit
        $this->logRoleDeletion($role);

        // Gérer les utilisateurs affectés
        $this->handleAffectedUsers($role);

        $role->delete();

        return redirect()->route('admin.roles.index')
            ->with('success', 'Rôle supprimé avec succès.');
    }

    public function preview(Request $request)
    {
        $permissions = $request->input('permissions', []);
        $preview = $this->generatePermissionPreview($permissions);
        return response()->json($preview);
    }

    public function analyze(Role $role)
    {
        $analysis = [
            'security_score' => $this->calculateSecurityScore($role),
            'permission_conflicts' => $this->detectPermissionConflicts($role),
            'unused_permissions' => $this->findUnusedPermissions($role),
            'suggested_optimizations' => $this->suggestOptimizations($role),
            'activity_patterns' => $this->analyzeActivityPatterns($role),
            'risk_assessment' => $this->assessSecurityRisks($role)
        ];

        return response()->json($analysis);
    }

    private function calculateActivityLevel($role)
    {
        // Calculer le niveau d'activité basé sur l'utilisation
        $lastUsed = $role->last_used ? Carbon::parse($role->last_used) : null;
        $usageFrequency = $role->users_count;
        
        if (!$lastUsed) return 'inactive';
        
        if ($lastUsed->isToday() && $usageFrequency > 5) return 'very_active';
        if ($lastUsed->isCurrentWeek() && $usageFrequency > 2) return 'active';
        if ($lastUsed->isCurrentMonth()) return 'moderately_active';
        return 'rarely_used';
    }

    private function getRecommendedTemplates()
    {
        return [
            [
                'name' => 'Gestionnaire de contenu',
                'permissions' => ['content.*', 'media.*'],
                'description' => 'Accès complet à la gestion de contenu'
            ],
            [
                'name' => 'Analyste',
                'permissions' => ['reports.*', 'analytics.*'],
                'description' => 'Accès aux rapports et analyses'
            ],
            [
                'name' => 'Support client',
                'permissions' => ['tickets.*', 'chat.*', 'customers.view'],
                'description' => 'Gestion du support client'
            ]
        ];
    }

    private function generateSecurityRules($role)
    {
        // Implémenter la génération de règles de sécurité
    }

    private function getRoleHistory($role)
    {
        // Récupérer l'historique des modifications
        return [];
    }

    private function analyzeRoleSecurity($role)
    {
        // Analyser la sécurité du rôle
        return [];
    }

    private function getUserStats($role)
    {
        // Obtenir les statistiques des utilisateurs
        return [];
    }

    private function logRoleChanges($role, $newData)
    {
        // Enregistrer les modifications
    }

    private function updateSecurityRules($role)
    {
        // Mettre à jour les règles de sécurité
    }

    private function notifyAffectedUsers($role)
    {
        // Notifier les utilisateurs des changements
    }

    private function logRoleDeletion($role)
    {
        // Enregistrer la suppression pour audit
    }

    private function handleAffectedUsers($role)
    {
        // Gérer les utilisateurs du rôle supprimé
    }

    private function generatePermissionPreview($permissions)
    {
        // Générer un aperçu des permissions
        return [];
    }

    private function calculateSecurityScore($role)
    {
        // Logique de calcul du score de sécurité
        $score = 0;
        
        // Vérifier le nombre de permissions
        $permissionCount = $role->permissions->count();
        if ($permissionCount > 0 && $permissionCount < 20) {
            $score += 30;
        } elseif ($permissionCount >= 20) {
            $score += 15; // Pénalité pour trop de permissions
        }

        // Vérifier les permissions sensibles
        $sensiblePermissions = ['delete', 'create', 'update'];
        $hasSensiblePermissions = $role->permissions->filter(function ($permission) use ($sensiblePermissions) {
            return Str::contains($permission->name, $sensiblePermissions);
        })->count();
        
        if ($hasSensiblePermissions > 0) {
            $score += 20;
        }

        // Bonus pour une description complète
        if (!empty($role->description) && strlen($role->description) > 50) {
            $score += 20;
        }

        // Bonus pour une utilisation active
        if ($role->users_count > 0) {
            $score += 30;
        }

        return min(100, $score); // Score maximum de 100
    }

    private function getSecurityScoreColor($score)
    {
        if ($score === null) return '#6B7280'; // Gris pour score inconnu
        if ($score >= 90) return '#10B981'; // Vert pour très bon score
        if ($score >= 70) return '#3B82F6'; // Bleu pour bon score
        if ($score >= 50) return '#F59E0B'; // Orange pour score moyen
        return '#EF4444'; // Rouge pour mauvais score
    }

    private function detectPermissionConflicts($role)
    {
        // Détecter les conflits de permissions
        return [];
    }

    private function findUnusedPermissions($role)
    {
        // Trouver les permissions non utilisées
        return [];
    }

    private function suggestOptimizations($role)
    {
        // Suggérer des optimisations
        return [];
    }

    private function analyzeActivityPatterns($role)
    {
        // Analyser les modèles d'activité
        return [];
    }

    private function assessSecurityRisks($role)
    {
        // Évaluer les risques de sécurité
        return [];
    }
}
