<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AdminProfileController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\RegionController;
use App\Http\Controllers\Admin\CityController;
use App\Http\Controllers\Admin\IronSpecificationController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\SaleController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\CementManager\TruckController as CementManagerTruckController;
use App\Http\Controllers\Admin\TruckController as AdminTruckController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Accountant\AccountantSupplyController;
use App\Http\Controllers\Accountant\AccountantDashboardController;
use App\Http\Controllers\Accountant\CementOrderController;
use App\Http\Controllers\Accountant\AccountantSupplierController;
use App\Http\Controllers\Accountant\AccountantDriverController;
use App\Http\Controllers\Accountant\DestinationController;
use App\Http\Controllers\Accountant\CementOrderStatsController;
use App\Http\Controllers\Accountant\CementOrderDetailController;
use App\Http\Controllers\Cashier\CashierController;
use App\Http\Controllers\Cashier\PaymentController;
use App\Http\Controllers\Accountant\ReportController as AccountantReportController;
use App\Http\Controllers\Accountant\ProfileController as AccountantProfileController;
use App\Http\Controllers\Accountant\SettingController as AccountantSettingController;
use App\Http\Controllers\Accountant\InvoiceController as AccountantInvoiceController;
use App\Http\Controllers\Accountant\PaymentController as AccountantPaymentController;
use App\Http\Controllers\Accountant\TaxController as AccountantTaxController;
use App\Http\Controllers\Accountant\CustomerController as AccountantCustomerController;
use App\Http\Controllers\Accountant\AccountantTruckController;
use App\Http\Controllers\Accountant\DriverTruckAssignmentController;
use App\Models\CementOrder;
use App\Http\Controllers\CementManager\CementManagerController;
use App\Http\Controllers\CementManager\SaleController as CementManagerSaleController;
use App\Http\Controllers\CementManager\DriverController;
use App\Http\Controllers\Admin\AdminDriverController;
use App\Http\Controllers\Admin\TogoRegionController;
use App\Http\Controllers\Accountant\AccountantRegionController;
use App\Http\Controllers\CementManager\SupplyController as CementManagerSupplyController;
use App\Http\Controllers\Admin\ReportController as AdminReportController;

// Routes d'authentification
Route::middleware('guest')->group(function () {
    Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('login', [LoginController::class, 'login']);
});

Route::middleware('auth')->group(function () {
    Route::post('logout', [LoginController::class, 'logout'])->name('logout');
});

// Redirection de la racine vers la page de connexion
Route::get('/', function () {
    if (Auth::check()) {
        $user = Auth::user();
        if ($user->hasRole('cement_manager')) {
            return redirect()->route('cement-manager.dashboard');
        }
        if ($user->hasRole('admin')) {
            return redirect()->route('admin.dashboard');
        }
        if ($user->hasRole('accountant')) {
            return redirect()->route('accountant.dashboard');
        }
    }
    return redirect()->route('login');
});

// Routes publiques nécessitant uniquement l'authentification
Route::middleware(['auth', 'web'])->group(function () {
    // Routes pour les régions du Togo
    Route::get('/togo-regions', [TogoRegionController::class, 'getRegionsWithCities'])->name('togo-regions');
    Route::get('/togo-regions/{region}/cities', [TogoRegionController::class, 'getCities'])->name('togo-regions.cities');
});

// Routes pour l'Admin
Route::prefix('admin')
    ->name('admin.')
    ->middleware(['auth', 'role:admin'])
    ->group(function () {
        // Dashboard
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
        
        // Routes pour le profil administrateur
        Route::get('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'show'])->name('profile.show');
        Route::get('/profile/edit', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('profile.edit');
        Route::put('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('profile.update');
        Route::get('/profile/password', [App\Http\Controllers\Admin\ProfileController::class, 'showPasswordForm'])->name('profile.password');
        Route::put('/profile/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('profile.password.update');

        // Route pour récupérer les villes d'une région
        Route::get('/regions/{region}/cities', [RegionController::class, 'getCities'])->name('regions.cities');
        
        // Routes pour les paramètres
        Route::get('/settings', [SettingController::class, 'index'])->name('settings.index');
        Route::put('/settings', [SettingController::class, 'update'])->name('settings.update');
        Route::post('/settings/backup', [SettingController::class, 'backup'])->name('settings.backup');
        Route::post('/settings/restore', [SettingController::class, 'restore'])->name('settings.restore');
        
        // Routes pour les rôles et permissions
        Route::prefix('roles')->name('roles.')->group(function () {
            Route::get('/', [RoleController::class, 'index'])->name('index');
            Route::get('/create', [RoleController::class, 'create'])->name('create');
            Route::post('/', [RoleController::class, 'store'])->name('store');
            Route::get('/{role}/edit', [RoleController::class, 'edit'])->name('edit');
            Route::put('/{role}', [RoleController::class, 'update'])->name('update');
            Route::delete('/{role}', [RoleController::class, 'destroy'])->name('destroy');
            Route::get('/{role}/analyze', [RoleController::class, 'analyze'])->name('analyze');
        });

        Route::prefix('permissions')->name('permissions.')->group(function () {
            Route::get('/', [PermissionController::class, 'index'])->name('index');
            Route::get('/create', [PermissionController::class, 'create'])->name('create');
            Route::post('/', [PermissionController::class, 'store'])->name('store');
            Route::get('/{permission}/edit', [PermissionController::class, 'edit'])->name('edit');
            Route::put('/{permission}', [PermissionController::class, 'update'])->name('update');
            Route::delete('/{permission}', [PermissionController::class, 'destroy'])->name('destroy');
        });
        
        // Routes pour les commandes
        Route::get('/orders', [OrderController::class, 'index'])->name('orders.index');
        Route::get('/orders/{order}', [OrderController::class, 'show'])->name('orders.show');
        Route::put('/orders/{order}', [OrderController::class, 'update'])->name('orders.update');
        Route::delete('/orders/{order}', [OrderController::class, 'destroy'])->name('orders.destroy');

        // Routes pour les utilisateurs
        Route::get('/users', [UserController::class, 'index'])->name('users.index');
        Route::get('/users/create', [UserController::class, 'create'])->name('users.create');

        Route::post('/users', [UserController::class, 'store'])->name('users.store');
        Route::get('/users/{user}/edit', [UserController::class, 'edit'])->name('users.edit');
        Route::put('/users/{user}', [UserController::class, 'update'])->name('users.update');
        Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy');
        Route::post('/users/{user}/toggle-active', [UserController::class, 'toggleActive'])->name('users.toggle-active');

        // Routes pour les categories
        Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
        Route::get('/categories/create', [CategoryController::class, 'create'])->name('categories.create');
        Route::post('/categories', [CategoryController::class, 'store'])->name('categories.store');
        Route::get('/categories/{category}/edit', [CategoryController::class, 'edit'])->name('categories.edit');
        Route::put('/categories/{category}', [CategoryController::class, 'update'])->name('categories.update');
        Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])->name('categories.destroy');

        // Routes pour les produits
        Route::get('/products', [ProductController::class, 'index'])->name('products.index');
        Route::get('/products/create', [ProductController::class, 'create'])->name('products.create');
        Route::post('/products', [ProductController::class, 'store'])->name('products.store');
        Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');
        Route::get('/products/{product}/edit', [ProductController::class, 'edit'])->name('products.edit');
        Route::put('/products/{product}', [ProductController::class, 'update'])->name('products.update');
        Route::delete('/products/{product}', [ProductController::class, 'destroy'])->name('products.destroy');

        // Routes pour les ventes
        Route::get('/sales', [SaleController::class, 'index'])->name('sales.index');
        Route::get('/sales/create', [SaleController::class, 'create'])->name('sales.create');
        Route::post('/sales', [SaleController::class, 'store'])->name('sales.store');
        Route::get('/sales/show/{sale}', [SaleController::class, 'showDetails'])->name('sales.show.details');
        Route::get('/sales/{sale}', [SaleController::class, 'show'])->name('sales.show');
        Route::get('/sales/{sale}/edit', [SaleController::class, 'edit'])->name('sales.edit');
        Route::put('/sales/{sale}', [SaleController::class, 'update'])->name('sales.update');
        Route::delete('/sales/{sale}', [SaleController::class, 'destroy'])->name('sales.destroy');
        Route::get('/sales/history', [SaleController::class, 'history'])->name('sales.history');
        Route::post('/sales/{sale}/validate-discount', [SaleController::class, 'validateDiscountSale'])->name('sales.validate-discount');

        // Actions en lot pour les ventes
        Route::post('/sales/export', [SaleController::class, 'export'])->name('sales.export');
        Route::post('/sales/export-selected', [SaleController::class, 'exportSelected'])->name('sales.export-selected');
        Route::post('/sales/bulk-mark-paid', [SaleController::class, 'bulkMarkPaid'])->name('sales.bulk-mark-paid');
        Route::post('/sales/delete-selected', [SaleController::class, 'deleteSelected'])->name('sales.delete-selected');
        Route::get('/sales/{sale}/invoice', [SaleController::class, 'generateInvoice'])->name('sales.invoice');

        // Approvisionnements
        Route::prefix('supplies')->name('supplies.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\SupplyController::class, 'index'])->name('index');
            Route::get('/{supply}', [App\Http\Controllers\Admin\SupplyController::class, 'show'])->name('show');
            Route::get('/{supply}/validate', [App\Http\Controllers\Admin\SupplyController::class, 'showValidateForm'])->name('showValidateForm');
            Route::post('/{supply}/validateSupply', [App\Http\Controllers\Admin\SupplyController::class, 'validateSupply'])->name('validateSupply');
            Route::get('/{supply}/reject', [App\Http\Controllers\Admin\SupplyController::class, 'showRejectForm'])->name('showRejectForm');
            Route::post('/{supply}/reject', [App\Http\Controllers\Admin\SupplyController::class, 'rejectSupply'])->name('rejectSupply');
        });

        // Routes pour la gestion des camions
        Route::prefix('trucks')->name('trucks.')->group(function () {
            Route::get('/', [AdminTruckController::class, 'index'])->name('index');
            Route::get('/create', [AdminTruckController::class, 'create'])->name('create');
            Route::post('/', [AdminTruckController::class, 'store'])->name('store');
            Route::get('/{truck}/edit', [AdminTruckController::class, 'edit'])->name('edit');
            Route::put('/{truck}', [AdminTruckController::class, 'update'])->name('update');
            Route::delete('/{truck}', [AdminTruckController::class, 'destroy'])->name('destroy');
            Route::post('/{truck}/toggle-status', [AdminTruckController::class, 'toggleStatus'])->name('toggle-status');
        });

        // Routes pour la gestion des chauffeurs
        Route::prefix('drivers')->name('drivers.')->group(function () {
            Route::get('/', [AdminDriverController::class, 'index'])->name('index');
            Route::get('/create', [AdminDriverController::class, 'create'])->name('create');
            Route::post('/', [AdminDriverController::class, 'store'])->name('store');
            Route::get('/{driver}/edit', [AdminDriverController::class, 'edit'])->name('edit');
            Route::put('/{driver}', [AdminDriverController::class, 'update'])->name('update');
            Route::delete('/{driver}', [AdminDriverController::class, 'destroy'])->name('destroy');

            Route::post('/{driver}/toggle-active', [AdminDriverController::class, 'toggleActive'])->name('toggle-active');
        });

        // Routes pour les rapports et analyses
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', [AdminReportController::class, 'index'])->name('index');

            // Rapport du chiffre d'affaires
            Route::get('/turnover', [AdminReportController::class, 'turnover'])->name('turnover');

            // Rapport des bénéfices
            Route::get('/profit', [AdminReportController::class, 'profit'])->name('profit');

            // Rapport des ventes
            Route::get('/sales', [AdminReportController::class, 'sales'])->name('sales');

            // Rapport des approvisionnements
            Route::get('/supplies', [AdminReportController::class, 'supplies'])->name('supplies');

            // Rapport des paiements
            Route::get('/payments', [AdminReportController::class, 'payments'])->name('payments');

            // Rapports supplémentaires
            Route::get('/low-stock', [AdminReportController::class, 'lowStock'])->name('low-stock');
            Route::get('/top-customers', [AdminReportController::class, 'topCustomers'])->name('top-customers');
            Route::get('/monthly-evolution', [AdminReportController::class, 'monthlyEvolution'])->name('monthly-evolution');
        });

        // Routes du profil administrateur
        Route::get('/profile', [AdminProfileController::class, 'show'])->name('profile.show');
        Route::get('/profile/edit', [AdminProfileController::class, 'edit'])->name('profile.edit');
        Route::put('/profile', [AdminProfileController::class, 'update'])->name('profile.update');
        Route::get('/profile/password', [AdminProfileController::class, 'showPasswordForm'])->name('profile.password');
        Route::put('/profile/password', [AdminProfileController::class, 'updatePassword'])->name('profile.password.update');
    });

// Routes pour l'Accountant
Route::prefix('accountant')
    ->middleware(['auth', 'role:accountant'])
    ->name('accountant.')
    ->group(function () {
        // Dashboard
        Route::get('/dashboard', [AccountantDashboardController::class, 'index'])->name('dashboard');
        Route::get('/dashboard-modern', [AccountantDashboardController::class, 'modernDashboard'])->name('dashboard.modern');
        Route::get('/dashboard-professional', [AccountantDashboardController::class, 'professionalDashboard'])->name('dashboard.professional');
        Route::get('/dashboard/stats', [AccountantDashboardController::class, 'getStats'])->name('dashboard.stats');
        Route::get('/dashboard/data', [AccountantDashboardController::class, 'getDashboardData'])->name('dashboard.data');
        Route::get('/dashboard/details', [AccountantDashboardController::class, 'getDetails'])->name('dashboard.details');
        Route::get('/dashboard/quick-stats', [AccountantDashboardController::class, 'getQuickStats'])->name('dashboard.quick-stats');
        Route::get('/dashboard/export', [AccountantDashboardController::class, 'exportDashboard'])->name('dashboard.export');
        Route::get('/customers/list', [AccountantDashboardController::class, 'getCustomersList'])->name('customers.list');
        Route::get('/supplies/my-supplies', [AccountantSupplyController::class, 'getMySupplies'])->name('supplies.my-supplies');

        // Profile
        Route::get('/profile', [AccountantProfileController::class, 'show'])->name('profile.show');
        Route::get('/profile/edit', [AccountantProfileController::class, 'edit'])->name('profile.edit');
        Route::put('/profile', [AccountantProfileController::class, 'update'])->name('profile.update');
        Route::get('/profile/password', [AccountantProfileController::class, 'showPasswordForm'])->name('profile.password');
        Route::put('/profile/password', [AccountantProfileController::class, 'updatePassword'])->name('profile.password.update');

        // Settings
        Route::get('/settings', [AccountantSettingController::class, 'index'])->name('settings.index');
        Route::put('/settings', [AccountantSettingController::class, 'update'])->name('settings.update');

        // Route pour récupérer les villes d'une région
        Route::get('/regions/{region}/cities', [AccountantRegionController::class, 'getCities'])->name('regions.cities');

        // Route pour récupérer les produits d'une catégorie
        Route::get('/supplies/products/category/{categoryId}', [AccountantSupplyController::class, 'getProductsByCategory'])->name('supplies.products.category');

        // Route pour la liste des camions disponibles
        Route::get('/trucks/list', [AccountantTruckController::class, 'getTrucksList'])->name('trucks.list');
        Route::get('/trucks/available', [AccountantTruckController::class, 'getAvailableTrucks'])->name('trucks.available');

        // Routes pour les commandes de ciment
        Route::prefix('cement-orders')->name('cement-orders.')->group(function () {
            Route::get('/', [CementOrderController::class, 'index'])->name('index');
            Route::get('/create', [CementOrderController::class, 'create'])->name('create');
            Route::get('/stats', [CementOrderStatsController::class, 'index'])->name('stats');
            Route::post('/', [CementOrderController::class, 'store'])->name('store');
            Route::get('/{cementOrder}', [CementOrderController::class, 'show'])->name('show');
            Route::get('/{cementOrder}/edit', [CementOrderController::class, 'edit'])->name('edit');
            Route::put('/{cementOrder}', [CementOrderController::class, 'update'])->name('update');
            Route::delete('/{cementOrder}', [CementOrderController::class, 'destroy'])->name('destroy');
        });

        // Routes pour les approvisionnements
        Route::prefix('supplies')->name('supplies.')->group(function () {
            Route::get('/', [AccountantSupplyController::class, 'index'])->name('index');
            Route::get('/create', [AccountantSupplyController::class, 'create'])->name('create');
            Route::post('/', [AccountantSupplyController::class, 'store'])->name('store');
            Route::get('/{supply}', [AccountantSupplyController::class, 'show'])->name('show');
            Route::get('/{supply}/edit', [AccountantSupplyController::class, 'edit'])->name('edit');
            Route::put('/{supply}', [AccountantSupplyController::class, 'update'])->name('update');
            Route::delete('/{supply}', [AccountantSupplyController::class, 'destroy'])->name('destroy');
            
            // Routes pour les données associées
            Route::get('/products/category/{category}', [AccountantSupplyController::class, 'getProductsByCategory'])->name('products.by.category');
            Route::get('/regions/{region}/cities', [AccountantSupplyController::class, 'getCitiesByRegion'])->name('cities.by.region');
            Route::get('/trucks/list', [AccountantSupplyController::class, 'getTrucks'])->name('trucks.list');
            
            // Validation et rejet
            Route::post('/{supply}/validate', [AccountantSupplyController::class, 'validateSupply'])->name('validate');
            Route::post('/{supply}/reject', [AccountantSupplyController::class, 'rejectSupply'])->name('reject');
        });

        // Routes pour les fournisseurs
        Route::prefix('suppliers')->name('suppliers.')->group(function () {
            Route::get('/', [AccountantSupplierController::class, 'index'])->name('index');
            Route::get('/create', [AccountantSupplierController::class, 'create'])->name('create');
            Route::post('/', [AccountantSupplierController::class, 'store'])->name('store');
            Route::get('/{supplier}', [AccountantSupplierController::class, 'show'])->name('show');
            Route::get('/{supplier}/edit', [AccountantSupplierController::class, 'edit'])->name('edit');
            Route::put('/{supplier}', [AccountantSupplierController::class, 'update'])->name('update');
            Route::delete('/{supplier}', [AccountantSupplierController::class, 'destroy'])->name('destroy');
        });

        // Routes pour les chauffeurs
        Route::prefix('drivers')->name('drivers.')->group(function () {
            Route::get('/', [AccountantDriverController::class, 'index'])->name('index');
            Route::get('/create', [AccountantDriverController::class, 'create'])->name('create');
            Route::post('/', [AccountantDriverController::class, 'store'])->name('store');
            Route::get('/{driver}', [AccountantDriverController::class, 'show'])->name('show');
            Route::get('/{driver}/edit', [AccountantDriverController::class, 'edit'])->name('edit');
            Route::put('/{driver}', [AccountantDriverController::class, 'update'])->name('update');
            Route::delete('/{driver}', [AccountantDriverController::class, 'destroy'])->name('destroy');
            Route::post('/{driver}/toggle-active', [AccountantDriverController::class, 'toggleActive'])->name('toggle-active');
            Route::post('/reassign-vehicle', [AccountantDriverController::class, 'reassignVehicle'])->name('reassign-vehicle');
        });

        // Routes pour les camions
        Route::prefix('trucks')->name('trucks.')->group(function () {
            Route::get('/', [AccountantTruckController::class, 'index'])->name('index');
            Route::get('/create', [AccountantTruckController::class, 'create'])->name('create');
            Route::post('/', [AccountantTruckController::class, 'store'])->name('store');
            Route::get('/{truck}', [AccountantTruckController::class, 'show'])->name('show');
            Route::get('/{truck}/edit', [AccountantTruckController::class, 'edit'])->name('edit');
            Route::put('/{truck}', [AccountantTruckController::class, 'update'])->name('update');
            Route::delete('/{truck}', [AccountantTruckController::class, 'destroy'])->name('destroy');
            Route::get('/list', [AccountantTruckController::class, 'getTrucks'])->name('list');
            Route::get('/available', [AccountantTruckController::class, 'getAvailableTrucks'])->name('available');
            Route::get('/assigned', [AccountantTruckController::class, 'getAssignedTrucks'])->name('assigned');
        });

        // Routes pour les recouvrements
        Route::prefix('recoveries')->name('recoveries.')->group(function () {
            Route::get('/', [App\Http\Controllers\Accountant\RecoveryController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Accountant\RecoveryController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Accountant\RecoveryController::class, 'store'])->name('store');
            Route::get('/{recovery}', [App\Http\Controllers\Accountant\RecoveryController::class, 'show'])->name('show');
            Route::get('/{recovery}/edit', [App\Http\Controllers\Accountant\RecoveryController::class, 'edit'])->name('edit');
            Route::put('/{recovery}', [App\Http\Controllers\Accountant\RecoveryController::class, 'update'])->name('update');
            Route::delete('/{recovery}', [App\Http\Controllers\Accountant\RecoveryController::class, 'destroy'])->name('destroy');
        });

        
        // Routes pour les paiements
        Route::prefix('payments')->name('payments.')->group(function () {
            Route::get('/', [AccountantPaymentController::class, 'index'])->name('index');
            Route::get('/create', [AccountantPaymentController::class, 'create'])->name('create');
            Route::post('/', [AccountantPaymentController::class, 'store'])->name('store');
            Route::get('/{payment}', [AccountantPaymentController::class, 'show'])->name('show');
            Route::get('/{payment}/edit', [AccountantPaymentController::class, 'edit'])->name('edit');
            Route::put('/{payment}', [AccountantPaymentController::class, 'update'])->name('update');
            Route::delete('/{payment}', [AccountantPaymentController::class, 'destroy'])->name('destroy');
        });

        // Route pour récupérer la liste des camions
        Route::get('/supplies/trucks/list', [AccountantSupplyController::class, 'getTrucks'])->name('supplies.trucks.list');
        
        // Routes pour les factures
        Route::prefix('invoices')->name('invoices.')->group(function () {
            Route::get('/', [AccountantInvoiceController::class, 'index'])->name('index');
            Route::get('/create', [AccountantInvoiceController::class, 'create'])->name('create');
            Route::post('/', [AccountantInvoiceController::class, 'store'])->name('store');
            Route::get('/{invoice}', [AccountantInvoiceController::class, 'show'])->name('show');
            Route::get('/{invoice}/edit', [AccountantInvoiceController::class, 'edit'])->name('edit');
            Route::put('/{invoice}', [AccountantInvoiceController::class, 'update'])->name('update');
            Route::delete('/{invoice}', [AccountantInvoiceController::class, 'destroy'])->name('destroy');
            Route::get('/{invoice}/print', [AccountantInvoiceController::class, 'print'])->name('print');
            Route::get('/{invoice}/download', [AccountantInvoiceController::class, 'download'])->name('download');
            Route::post('/{invoice}/send', [AccountantInvoiceController::class, 'send'])->name('send');
        });
        
        // Routes pour les ventes
        Route::prefix('sales')->name('sales.')->group(function () {
            Route::get('/', [App\Http\Controllers\Accountant\SaleController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Accountant\SaleController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Accountant\SaleController::class, 'store'])->name('store');
            Route::get('/{sale}', [App\Http\Controllers\Accountant\SaleController::class, 'show'])->name('show');
            Route::get('/{sale}/edit', [App\Http\Controllers\Accountant\SaleController::class, 'edit'])->name('edit');
            Route::put('/{sale}', [App\Http\Controllers\Accountant\SaleController::class, 'update'])->name('update');
            Route::delete('/{sale}', [App\Http\Controllers\Accountant\SaleController::class, 'destroy'])->name('destroy');
        });
        
        // Routes pour les factures
        Route::prefix('invoices')->name('invoices.')->group(function () {
            Route::get('/', [App\Http\Controllers\Accountant\InvoiceController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Accountant\InvoiceController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Accountant\InvoiceController::class, 'store'])->name('store');
            Route::get('/{invoice}', [App\Http\Controllers\Accountant\InvoiceController::class, 'show'])->name('show');
            Route::get('/{invoice}/edit', [App\Http\Controllers\Accountant\InvoiceController::class, 'edit'])->name('edit');
            Route::put('/{invoice}', [App\Http\Controllers\Accountant\InvoiceController::class, 'update'])->name('update');
            Route::delete('/{invoice}', [App\Http\Controllers\Accountant\InvoiceController::class, 'destroy'])->name('destroy');
            Route::post('/{invoice}/send', [App\Http\Controllers\Accountant\InvoiceController::class, 'send'])->name('send');
            Route::get('/{invoice}/print', [App\Http\Controllers\Accountant\InvoiceController::class, 'print'])->name('print');
        });

        // Routes pour les rapports financiers
        Route::prefix('reports')->name('reports.')->group(function () {
            Route::get('/', [App\Http\Controllers\Accountant\ReportController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Accountant\ReportController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Accountant\ReportController::class, 'store'])->name('store');

            // Routes pour les rapports spécifiques
            Route::get('/sales', [App\Http\Controllers\Accountant\ReportController::class, 'salesReport'])->name('sales');
            Route::get('/revenue', [App\Http\Controllers\Accountant\ReportController::class, 'revenue'])->name('revenue');
            Route::get('/customers', [App\Http\Controllers\Accountant\ReportController::class, 'customers'])->name('customers');
            Route::get('/supplies', [App\Http\Controllers\Accountant\ReportController::class, 'suppliesReport'])->name('supplies');
            Route::get('/payments', [App\Http\Controllers\Accountant\ReportController::class, 'paymentsReport'])->name('payments');
            Route::get('/generate', [App\Http\Controllers\Accountant\ReportController::class, 'generateReport'])->name('generate');

            Route::get('/{report}', [App\Http\Controllers\Accountant\ReportController::class, 'show'])->name('show');
            Route::get('/{report}/edit', [App\Http\Controllers\Accountant\ReportController::class, 'edit'])->name('edit');
            Route::put('/{report}', [App\Http\Controllers\Accountant\ReportController::class, 'update'])->name('update');
            Route::delete('/{report}', [App\Http\Controllers\Accountant\ReportController::class, 'destroy'])->name('destroy');
            Route::get('/types/{type}', [App\Http\Controllers\Accountant\ReportController::class, 'getByType'])->name('types');
            Route::get('/{report}/export', [App\Http\Controllers\Accountant\ReportController::class, 'export'])->name('export');
            Route::get('/{report}/print', [App\Http\Controllers\Accountant\ReportController::class, 'print'])->name('print');
        });

        // Routes pour les dépenses
        Route::prefix('expenses')->name('expenses.')->group(function () {
            Route::get('/', [App\Http\Controllers\Accountant\ExpenseController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Accountant\ExpenseController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Accountant\ExpenseController::class, 'store'])->name('store');
            Route::get('/{expense}', [App\Http\Controllers\Accountant\ExpenseController::class, 'show'])->name('show');
            Route::get('/{expense}/edit', [App\Http\Controllers\Accountant\ExpenseController::class, 'edit'])->name('edit');
            Route::put('/{expense}', [App\Http\Controllers\Accountant\ExpenseController::class, 'update'])->name('update');
            Route::delete('/{expense}', [App\Http\Controllers\Accountant\ExpenseController::class, 'destroy'])->name('destroy');
            Route::get('/category/{category}', [App\Http\Controllers\Accountant\ExpenseController::class, 'byCategory'])->name('byCategory');
            Route::get('/{expense}/print', [App\Http\Controllers\Accountant\ExpenseController::class, 'print'])->name('print');
        });

        // Routes pour les clients
        Route::prefix('customers')->name('customers.')->group(function () {
            Route::get('/', [AccountantCustomerController::class, 'index'])->name('index');
            Route::get('/create', [AccountantCustomerController::class, 'create'])->name('create');
            Route::post('/', [AccountantCustomerController::class, 'store'])->name('store');
            Route::get('/{customer}', [AccountantCustomerController::class, 'show'])->name('show');
            Route::get('/{customer}/edit', [AccountantCustomerController::class, 'edit'])->name('edit');
            Route::put('/{customer}', [AccountantCustomerController::class, 'update'])->name('update');
            Route::delete('/{customer}', [AccountantCustomerController::class, 'destroy'])->name('destroy');
        });
        
        // Routes pour les exports
        Route::prefix('exports')->name('exports.')->group(function () {
            Route::get('/', [App\Http\Controllers\Accountant\ExportController::class, 'index'])->name('index');
            Route::post('/sales', [App\Http\Controllers\Accountant\ExportController::class, 'exportSales'])->name('sales');
            Route::post('/supplies', [App\Http\Controllers\Accountant\ExportController::class, 'exportSupplies'])->name('supplies');
            Route::post('/payments', [App\Http\Controllers\Accountant\ExportController::class, 'exportPayments'])->name('payments');
            Route::post('/customers', [App\Http\Controllers\Accountant\ExportController::class, 'exportCustomers'])->name('customers');
        });
        
        // Route pour l'aide
        Route::get('/help', [App\Http\Controllers\Accountant\HelpController::class, 'index'])->name('help');
        
        // Route directe pour les paramètres (pour compatibilité)
        Route::get('/settings', [App\Http\Controllers\Accountant\SettingsController::class, 'index'])->name('settings');
        
        // Routes détaillées pour les paramètres
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Accountant\SettingsController::class, 'index'])->name('index');
            Route::put('/profile', [App\Http\Controllers\Accountant\SettingsController::class, 'updateProfile'])->name('profile.update');
            Route::put('/password', [App\Http\Controllers\Accountant\SettingsController::class, 'updatePassword'])->name('password.update');
            Route::put('/preferences', [App\Http\Controllers\Accountant\SettingsController::class, 'updatePreferences'])->name('preferences.update');
            Route::put('/notifications', [App\Http\Controllers\Accountant\SettingsController::class, 'updateNotifications'])->name('notifications.update');
        });

        // Route pour les camions disponibles
        // Supprimer la route en double des camions disponibles
        // Route::get('/api/trucks/available', [App\Http\Controllers\Api\TruckController::class, 'getAvailableTrucks'])
        //     ->name('trucks.available');
    });

// Routes pour le Cement Manager
Route::prefix('cement-manager')
    ->name('cement-manager.')
    ->middleware(['auth', 'cement_manager'])
    ->group(function () {
        Route::get('/', [CementManagerController::class, 'index'])->name('orders');
        Route::get('/dashboard', [CementManagerController::class, 'dashboard'])->name('dashboard');
        
        // Routes du profil
        Route::get('/profile', [App\Http\Controllers\CementManager\ProfileController::class, 'show'])->name('profile.show');
        Route::get('/profile/edit', [App\Http\Controllers\CementManager\ProfileController::class, 'edit'])->name('profile.edit');
        Route::put('/profile', [App\Http\Controllers\CementManager\ProfileController::class, 'update'])->name('profile.update');
        Route::get('/profile/password', [App\Http\Controllers\CementManager\ProfileController::class, 'showPasswordForm'])->name('profile.password');
        Route::put('/profile/password', [App\Http\Controllers\CementManager\ProfileController::class, 'updatePassword'])->name('profile.password.update');
        
        // Routes pour les approvisionnements
        Route::prefix('supplies')->name('supplies.')->group(function () {
            Route::get('/', [CementManagerSupplyController::class, 'index'])->name('index');
            Route::get('/{supply}', [CementManagerSupplyController::class, 'show'])->name('show');
        });

        // Routes pour les ventes
        Route::get('sales/supply-details', [CementManagerSaleController::class, 'getSupplyDetails'])->name('sales.supply-details');
        Route::patch('sales/{sale}/delivery-status', [CementManagerSaleController::class, 'updateDeliveryStatus'])->name('sales.delivery-status');
        Route::post('sales/{sale}/process-payment', [CementManagerSaleController::class, 'processPayment'])->name('sales.process-payment');
        Route::get('check-rejected-sales', [CementManagerSaleController::class, 'checkRejectedSales'])->name('check-rejected-sales');
        Route::get('get-supply-card-data', [CementManagerSaleController::class, 'getSupplyCardData'])->name('get-supply-card-data');
        Route::resource('sales', CementManagerSaleController::class);

        // Routes pour les camions
        Route::get('/trucks', [CementManagerTruckController::class, 'index'])->name('trucks.index');
        Route::get('/trucks/create', [CementManagerTruckController::class, 'create'])->name('trucks.create');
        Route::post('/trucks', [CementManagerTruckController::class, 'store'])->name('trucks.store');
        Route::get('/trucks/{truck}/edit', [CementManagerTruckController::class, 'edit'])->name('trucks.edit');
        Route::put('/trucks/{truck}', [CementManagerTruckController::class, 'update'])->name('trucks.update');
        Route::delete('/trucks/{truck}', [CementManagerTruckController::class, 'destroy'])->name('trucks.destroy');
        Route::get('/trucks/{truck}', [CementManagerTruckController::class, 'show'])->name('trucks.show');
    });

// Routes pour le Iron Manager (temporairement commentées - contrôleurs manquants)
/*
Route::prefix('iron-manager')
    ->name('iron-manager.')
    ->middleware(['auth', 'role:iron_manager'])
    ->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\IronManager\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/profile', [App\Http\Controllers\IronManager\ProfileController::class, 'index'])->name('profile');
        Route::get('/orders', [App\Http\Controllers\IronManager\OrderController::class, 'index'])->name('orders.index');
        Route::get('/inventory', [App\Http\Controllers\IronManager\InventoryController::class, 'index'])->name('inventory.index');
    });
*/

// Routes pour le Cashier
Route::prefix('cashier')
    ->name('cashier.')
    ->middleware(['auth', 'role:cashier'])
    ->group(function () {
        // Dashboard
        Route::get('/dashboard', [CashierController::class, 'dashboard'])->name('dashboard');

        // Profile
        Route::get('/profile', [App\Http\Controllers\Cashier\ProfileController::class, 'show'])->name('profile');
        Route::get('/profile/edit', [App\Http\Controllers\Cashier\ProfileController::class, 'edit'])->name('profile.edit');
        Route::put('/profile', [App\Http\Controllers\Cashier\ProfileController::class, 'update'])->name('profile.update');
        Route::get('/profile/password', [App\Http\Controllers\Cashier\ProfileController::class, 'showPasswordForm'])->name('profile.password');
        Route::put('/profile/password', [App\Http\Controllers\Cashier\ProfileController::class, 'updatePassword'])->name('profile.password.update');
        
        // Système de règlement des ventes
        Route::prefix('payments')->name('payments.')->group(function () {
            // Liste des paiements
            Route::get('/', [PaymentController::class, 'index'])->name('index');
            
            // Ventes en attente de paiement
            Route::get('/pending-sales', [PaymentController::class, 'pendingSales'])->name('pending');
            Route::get('/process/{sale}', [PaymentController::class, 'processSale'])->name('process');
            Route::post('/process/{sale}', [PaymentController::class, 'storePayment'])->name('store');
            
            // Échéanciers de paiement
            Route::get('/schedule/{sale}/create', [PaymentController::class, 'createSchedule'])->name('schedule.create');
            Route::post('/schedule/{sale}', [PaymentController::class, 'storeSchedule'])->name('schedule.store');
            
            // Ces routes doivent être après les routes spécifiques pour éviter les conflits
            Route::get('/receipt/{payment}', [PaymentController::class, 'showReceipt'])->name('receipt');
            Route::get('/{payment}', [PaymentController::class, 'show'])->name('show');
        });

        // Commandes de ciment
        Route::get('/cement-orders', [App\Http\Controllers\Cashier\CementOrderController::class, 'index'])->name('cement-orders.index');
        Route::get('/cement-orders/create', [App\Http\Controllers\Cashier\CementOrderController::class, 'create'])->name('cement-orders.create');
        Route::post('/cement-orders', [App\Http\Controllers\Cashier\CementOrderController::class, 'store'])->name('cement-orders.store');
        Route::get('/cement-orders/{order}', [App\Http\Controllers\Cashier\CementOrderController::class, 'show'])->name('cement-orders.show');
        Route::post('/cement-orders/convert-assignment/{assignment}', [App\Http\Controllers\Cashier\CementOrderController::class, 'convertAssignment'])
            ->name('cement-orders.convert-assignment');
        Route::post('/cement-orders/{order}/convert-to-credit/{trip_id}', [App\Http\Controllers\Cashier\CementOrderController::class, 'convertToCredit'])
            ->name('cement-orders.convert-to-credit');
        Route::post('/cement-orders/{order}/process-payment', [App\Http\Controllers\Cashier\CementOrderController::class, 'processPayment'])
            ->name('cement-orders.process-payment');
        
        // Ventes à crédit
        Route::get('/credit-sales', [App\Http\Controllers\Cashier\CreditSaleController::class, 'index'])->name('credit-sales.index');
        Route::get('/credit-sales/{sale}', [App\Http\Controllers\Cashier\CreditSaleController::class, 'show'])->name('credit-sales.show');
        Route::post('/credit-sales/{sale}/record-payment', [App\Http\Controllers\Cashier\CreditSaleController::class, 'recordPayment'])
            ->name('credit-sales.record-payment');
        
        // Paiements
        Route::get('/payments', [App\Http\Controllers\Cashier\PaymentController::class, 'index'])->name('payments.index');
        Route::get('/payments/{payment}', [App\Http\Controllers\Cashier\PaymentController::class, 'show'])->name('payments.show');

        // Ventes
        Route::get('/sales', [App\Http\Controllers\Cashier\SaleController::class, 'index'])->name('sales.index');
        Route::get('/sales/create', [App\Http\Controllers\Cashier\SaleController::class, 'create'])->name('sales.create');
        Route::post('/sales', [App\Http\Controllers\Cashier\SaleController::class, 'store'])->name('sales.store');
        
        // Nouvelle interface redessinée
        Route::get('/sales/redesign', [App\Http\Controllers\Cashier\SalesRedesignController::class, 'index'])->name('sales.redesign');
        
        // Interface moderne des ventes (temporaire pour test)
        Route::get('/sales/modern', function() {
            $sales = App\Models\Sale::latest()->paginate(10);
            return view('cashier.sales.modern-index', compact('sales'));
        })->name('sales.modern');
        
        Route::get('/sales/{sale}', [App\Http\Controllers\Cashier\SaleController::class, 'show'])->name('sales.show');
        Route::get('/sales/{sale}/edit', [App\Http\Controllers\Cashier\SaleController::class, 'edit'])->name('sales.edit');
        Route::put('/sales/{sale}', [App\Http\Controllers\Cashier\SaleController::class, 'update'])->name('sales.update');
        Route::delete('/sales/{sale}', [App\Http\Controllers\Cashier\SaleController::class, 'destroy'])->name('sales.destroy');
        
        // Nouvelle interface moderne des ventes
        Route::get('/sales/modern', function() {
            $controller = app()->make(App\Http\Controllers\Cashier\SaleController::class);
            return $controller->modern();
        })->name('sales.modern');
    });

// Routes pour le Customer (temporairement commentées - contrôleurs manquants)
/*
Route::prefix('customer')
    ->name('customer.')
    ->middleware(['auth', 'role:customer'])
    ->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Customer\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/profile', [App\Http\Controllers\Customer\ProfileController::class, 'index'])->name('profile');
        Route::get('/orders', [App\Http\Controllers\Customer\OrderController::class, 'index'])->name('orders.index');
        Route::get('/invoices', [App\Http\Controllers\Customer\InvoiceController::class, 'index'])->name('invoices.index');
    });

// Routes pour le Customer Service
Route::prefix('customer-service')
    ->name('customer-service.')
    ->middleware(['auth', 'role:customer_service'])
    ->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\CustomerService\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/profile', [App\Http\Controllers\CustomerService\ProfileController::class, 'index'])->name('profile');
        Route::get('/tickets', [App\Http\Controllers\CustomerService\TicketController::class, 'index'])->name('tickets.index');
        Route::get('/customers', [App\Http\Controllers\CustomerService\CustomerController::class, 'index'])->name('customers.index');
    });

// Routes communes pour les rôles qui peuvent gérer les clients
Route::middleware(['auth', 'role:accountant|cashier'])
    ->name('common.')
    ->group(function () {
        Route::post('/customers', [\App\Http\Controllers\Common\CustomerController::class, 'store'])->name('customers.store');
    });
*/

// Routes protégées par le middleware auth
Route::middleware('auth')->group(function () {
    // Routes partagées entre cement_manager et accountant
    Route::middleware('cement_manager_or_accountant')->group(function () {
    });
});

// Routes API communes pour tous les rôles
Route::middleware(['auth'])->group(function () {
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/trucks/available', [App\Http\Controllers\Api\TruckController::class, 'getAvailableTrucks'])
            ->name('trucks.available')
            ->middleware(['role:admin|accountant|cement_manager|cashier']);
    });
});

// Route temporaire pour tester l'affichage des camions assignés
Route::get('/test/trucks/assigned', function() {
    $trucks = \App\Models\Truck::with(['driver', 'capacity'])
        ->where('status', 'assigned')
        ->get()
        ->map(function($truck) {
            return [
                'immatriculation' => $truck->registration_number,
                'marque' => $truck->brand,
                'modele' => $truck->model,
                'chauffeur' => [
                    'nom' => $truck->driver->last_name,
                    'prenom' => $truck->driver->first_name
                ],
                'capacite' => [
                    'valeur' => $truck->capacity->capacity,
                    'unite' => $truck->capacity->unit
                ],
                'statut' => $truck->status
            ];
        });
    
    return response()->json($trucks);
});

// Route de test pour l'image
Route::get('/test-image', function () {
    return view('test-image');
});

// Fallback route
Route::fallback(function () {
    return redirect('/login');
});
