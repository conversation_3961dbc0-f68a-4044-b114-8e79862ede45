<?php $__env->startSection('title', 'Ajouter un Chauffeur'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header avec breadcrumb -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-2">
                    <li class="breadcrumb-item">
                        <a href="<?php echo e(route('admin.drivers.index')); ?>" class="text-decoration-none">
                            <i class="fas fa-users me-1"></i> Chauffeurs
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Nouveau chauffeur</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-plus text-success me-2"></i>
                Ajouter un Nouveau Chauffeur
            </h1>
            <p class="text-muted mb-0">Enregistrez un nouveau chauffeur dans votre équipe</p>
        </div>
        <div>
            <a href="<?php echo e(route('admin.drivers.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Retour à la liste
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-12">
            <!-- Formulaire principal -->
            <form id="driverForm" action="<?php echo e(route('admin.drivers.store')); ?>" method="POST" class="needs-validation" novalidate>
                <?php echo csrf_field(); ?>

                <!-- Section 1: Informations personnelles -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-primary text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-user me-2"></i>
                            Informations Personnelles
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name" class="form-label fw-bold">
                                        <i class="fas fa-user text-primary me-1"></i>
                                        Prénom <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-user text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control form-control-lg <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="first_name"
                                               name="first_name"
                                               value="<?php echo e(old('first_name')); ?>"
                                               placeholder="Ex: Jean"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Parfait !
                                        </div>
                                        <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Prénom du chauffeur
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name" class="form-label fw-bold">
                                        <i class="fas fa-user text-primary me-1"></i>
                                        Nom de famille <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-user text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control form-control-lg <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="last_name"
                                               name="last_name"
                                               value="<?php echo e(old('last_name')); ?>"
                                               placeholder="Ex: Dupont"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Parfait !
                                        </div>
                                        <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Nom de famille du chauffeur
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Aperçu du nom complet -->
                        <div class="mt-3">
                            <div class="alert alert-info border-left-info" id="namePreview" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-preview me-3">
                                        <div class="avatar-circle bg-primary" id="avatarPreview">
                                            --
                                        </div>
                                    </div>
                                    <div>
                                        <strong>Nom complet :</strong> <span id="fullNamePreview">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 2: Informations de contact -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-info text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-address-book me-2"></i>
                            Informations de Contact
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label fw-bold">
                                        <i class="fas fa-envelope text-info me-1"></i>
                                        Adresse Email <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-at text-muted"></i>
                                        </span>
                                        <input type="email"
                                               class="form-control form-control-lg <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="email"
                                               name="email"
                                               value="<?php echo e(old('email')); ?>"
                                               placeholder="Ex: <EMAIL>"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Email valide !
                                        </div>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Adresse email professionnelle du chauffeur
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label fw-bold">
                                        <i class="fas fa-phone text-success me-1"></i>
                                        Numéro de Téléphone <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-mobile-alt text-muted"></i>
                                        </span>
                                        <input type="tel"
                                               class="form-control form-control-lg <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="phone"
                                               name="phone"
                                               value="<?php echo e(old('phone')); ?>"
                                               placeholder="Ex: +33 6 12 34 56 78"
                                               pattern="[+]?[0-9\s\-\(\)]+"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Numéro valide !
                                        </div>
                                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Numéro de téléphone mobile du chauffeur
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row g-4 mt-2">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="address" class="form-label fw-bold">
                                        <i class="fas fa-map-marker-alt text-warning me-1"></i>
                                        Adresse Complète
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-home text-muted"></i>
                                        </span>
                                        <textarea class="form-control form-control-lg <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                  id="address"
                                                  name="address"
                                                  rows="3"
                                                  placeholder="Ex: 123 Rue de la République&#10;75001 Paris, France"><?php echo e(old('address')); ?></textarea>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Adresse enregistrée !
                                        </div>
                                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Adresse de résidence du chauffeur (optionnel)
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 3: Informations de permis -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-warning text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-id-card me-2"></i>
                            Informations de Permis de Conduire
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="license_number" class="form-label fw-bold">
                                        <i class="fas fa-id-badge text-warning me-1"></i>
                                        Numéro de Permis <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-hashtag text-muted"></i>
                                        </span>
                                        <input type="text"
                                               class="form-control form-control-lg <?php $__errorArgs = ['license_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="license_number"
                                               name="license_number"
                                               value="<?php echo e(old('license_number')); ?>"
                                               placeholder="Ex: 123456789012"
                                               pattern="[A-Z0-9]+"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Numéro valide !
                                        </div>
                                        <?php $__errorArgs = ['license_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Numéro unique du permis de conduire
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="license_expiry" class="form-label fw-bold">
                                        <i class="fas fa-calendar-times text-danger me-1"></i>
                                        Date d'Expiration <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light">
                                            <i class="fas fa-calendar text-muted"></i>
                                        </span>
                                        <input type="date"
                                               class="form-control form-control-lg <?php $__errorArgs = ['license_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="license_expiry"
                                               name="license_expiry"
                                               value="<?php echo e(old('license_expiry')); ?>"
                                               min="<?php echo e(date('Y-m-d', strtotime('+1 day'))); ?>"
                                               required>
                                        <div class="valid-feedback">
                                            <i class="fas fa-check me-1"></i> Date valide !
                                        </div>
                                        <?php $__errorArgs = ['license_expiry'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <small class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Date d'expiration du permis (doit être future)
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Indicateur de validité du permis -->
                        <div class="mt-3">
                            <div class="alert alert-success border-left-success" id="licenseValidityAlert" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-shield-alt fa-2x me-3"></i>
                                    <div>
                                        <strong>Validité du permis :</strong> <span id="licenseValidityText">-</span><br>
                                        <small class="text-muted" id="licenseValidityDetails">-</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 4: Configuration et Notes -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-success text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-cog me-2"></i>
                            Configuration et Informations Complémentaires
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label fw-bold">
                                        <i class="fas fa-traffic-light text-success me-1"></i>
                                        Statut Initial <span class="text-danger">*</span>
                                    </label>
                                    <div class="status-options">
                                        <div class="form-check form-check-card mb-3">
                                            <input class="form-check-input" type="radio" name="status" id="status_available"
                                                   value="available" <?php echo e(old('status', 'available') == 'available' ? 'checked' : ''); ?> required>
                                            <label class="form-check-label status-card available" for="status_available">
                                                <div class="status-icon">
                                                    <i class="fas fa-user-check"></i>
                                                </div>
                                                <div class="status-content">
                                                    <div class="status-title">Disponible</div>
                                                    <div class="status-description">Prêt pour les missions</div>
                                                </div>
                                            </label>
                                        </div>

                                        <div class="form-check form-check-card mb-3">
                                            <input class="form-check-input" type="radio" name="status" id="status_unavailable"
                                                   value="unavailable" <?php echo e(old('status') == 'unavailable' ? 'checked' : ''); ?>>
                                            <label class="form-check-label status-card unavailable" for="status_unavailable">
                                                <div class="status-icon">
                                                    <i class="fas fa-user-times"></i>
                                                </div>
                                                <div class="status-content">
                                                    <div class="status-title">Indisponible</div>
                                                    <div class="status-description">En congé ou indisponible</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notes" class="form-label fw-bold">
                                        <i class="fas fa-sticky-note text-warning me-1"></i>
                                        Notes et Observations
                                    </label>
                                    <textarea class="form-control form-control-lg <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="notes"
                                              name="notes"
                                              rows="6"
                                              placeholder="Ajoutez des informations complémentaires sur le chauffeur...&#10;&#10;Exemples :&#10;- Expérience de conduite&#10;- Spécialisations&#10;- Formations suivies&#10;- Remarques particulières"><?php echo e(old('notes')); ?></textarea>
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Informations utiles pour la gestion du chauffeur (optionnel)
                                    </div>
                                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-triangle me-1"></i> <?php echo e($message); ?>

                                        </div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section 5: Résumé et Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-gradient-secondary text-white">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-clipboard-check me-2"></i>
                            Validation et Enregistrement
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Résumé des informations -->
                        <div class="alert alert-info border-left-info" id="formSummary" style="display: none;">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-info-circle fa-lg me-2"></i>
                                <h6 class="mb-0">Résumé du chauffeur</h6>
                            </div>
                            <div id="summaryContent"></div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="button" class="btn btn-outline-secondary" id="previewBtn">
                                    <i class="fas fa-eye me-1"></i> Aperçu
                                </button>
                                <button type="button" class="btn btn-outline-info" id="resetBtn">
                                    <i class="fas fa-undo me-1"></i> Réinitialiser
                                </button>
                            </div>
                            <div>
                                <a href="<?php echo e(route('admin.drivers.index')); ?>" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-1"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-success btn-lg" id="submitBtn">
                                    <i class="fas fa-save me-1"></i>
                                    <span class="btn-text">Enregistrer le Chauffeur</span>
                                    <span class="spinner-border spinner-border-sm ms-2" role="status" style="display: none;"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light fixed-bottom">
    <div class="container text-center">
        <span class="text-muted"> <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Styles généraux */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .bg-gradient-info {
        background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
    }
    .bg-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .bg-gradient-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
    .bg-gradient-secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    /* Bordures colorées */
    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }
    .border-left-success {
        border-left: 4px solid #1cc88a !important;
    }
    .border-left-warning {
        border-left: 4px solid #f6c23e !important;
    }
    .border-left-danger {
        border-left: 4px solid #e74a3b !important;
    }

    /* Breadcrumb personnalisé */
    .breadcrumb {
        background: transparent;
        padding: 0;
    }
    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-weight: bold;
    }

    /* Cartes de formulaire */
    .card {
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }
    .card-header {
        border: none;
        padding: 1.5rem;
    }
    .card-body {
        padding: 2rem;
    }

    /* Groupes d'entrée améliorés */
    .input-group-text {
        border: 1px solid #e3e6f0;
        background-color: #f8f9fc;
    }
    .form-control, .form-select {
        border: 1px solid #e3e6f0;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    .form-control:focus, .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    .form-control-lg, .form-select-lg {
        padding: 1rem 1.25rem;
        font-size: 1.1rem;
    }

    /* Labels améliorés */
    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.75rem;
    }

    /* Avatar preview */
    .avatar-preview .avatar-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1rem;
        text-transform: uppercase;
    }

    /* Cartes de statut radio */
    .status-options {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    .form-check-card {
        margin-bottom: 0 !important;
    }
    .form-check-input {
        display: none;
    }
    .status-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 2px solid #e3e6f0;
        border-radius: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }
    .status-card:hover {
        border-color: #4e73df;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .form-check-input:checked + .status-card.available {
        border-color: #1cc88a;
        background: linear-gradient(135deg, rgba(28, 200, 138, 0.1) 0%, rgba(28, 200, 138, 0.05) 100%);
    }
    .form-check-input:checked + .status-card.unavailable {
        border-color: #e74a3b;
        background: linear-gradient(135deg, rgba(231, 74, 59, 0.1) 0%, rgba(231, 74, 59, 0.05) 100%);
    }

    .status-icon {
        font-size: 1.5rem;
        margin-right: 1rem;
        width: 40px;
        text-align: center;
    }
    .status-card.available .status-icon {
        color: #1cc88a;
    }
    .status-card.unavailable .status-icon {
        color: #e74a3b;
    }

    .status-title {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }
    .status-description {
        font-size: 0.875rem;
        color: #6c757d;
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
    }
    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1.1rem;
    }
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Validation feedback amélioré */
    .valid-feedback, .invalid-feedback {
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
    .valid-feedback {
        color: #1cc88a;
    }

    /* Animations */
    .card {
        animation: slideInUp 0.5s ease-out;
    }
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .card-body {
            padding: 1.5rem;
        }
        .status-options {
            gap: 0.5rem;
        }
        .status-card {
            padding: 0.75rem;
        }
        .status-icon {
            font-size: 1.25rem;
            margin-right: 0.75rem;
            width: 30px;
        }
        .avatar-preview .avatar-circle {
            width: 40px;
            height: 40px;
            font-size: 0.875rem;
        }
    }

    /* Indicateur de chargement */
    .btn .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* Textarea amélioré */
    textarea.form-control {
        resize: vertical;
        min-height: 120px;
    }

    /* Alertes personnalisées */
    .alert {
        border-radius: 0.75rem;
        border: none;
    }

    /* Validation de permis */
    .license-validity-valid {
        border-left-color: #1cc88a !important;
        background: linear-gradient(135deg, rgba(28, 200, 138, 0.1) 0%, rgba(28, 200, 138, 0.05) 100%);
    }
    .license-validity-warning {
        border-left-color: #f6c23e !important;
        background: linear-gradient(135deg, rgba(246, 194, 62, 0.1) 0%, rgba(246, 194, 62, 0.05) 100%);
    }
    .license-validity-danger {
        border-left-color: #e74a3b !important;
        background: linear-gradient(135deg, rgba(231, 74, 59, 0.1) 0%, rgba(231, 74, 59, 0.05) 100%);
    }

    /* Amélioration des placeholders */
    .form-control::placeholder {
        color: #a0aec0;
        font-style: italic;
    }

    /* Styles pour les icônes colorées */
    .text-primary { color: #4e73df !important; }
    .text-info { color: #36b9cc !important; }
    .text-warning { color: #f6c23e !important; }
    .text-success { color: #1cc88a !important; }
    .text-danger { color: #e74a3b !important; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('driverForm');
    const submitBtn = document.getElementById('submitBtn');
    const previewBtn = document.getElementById('previewBtn');
    const resetBtn = document.getElementById('resetBtn');
    const formSummary = document.getElementById('formSummary');
    const summaryContent = document.getElementById('summaryContent');
    const namePreview = document.getElementById('namePreview');
    const fullNamePreview = document.getElementById('fullNamePreview');
    const avatarPreview = document.getElementById('avatarPreview');
    const licenseValidityAlert = document.getElementById('licenseValidityAlert');
    const licenseValidityText = document.getElementById('licenseValidityText');
    const licenseValidityDetails = document.getElementById('licenseValidityDetails');

    // Validation en temps réel
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            validateField(this);
            updateNamePreview();
            updateLicenseValidity();
            updateFormSummary();
        });
        input.addEventListener('change', function() {
            validateField(this);
            updateNamePreview();
            updateLicenseValidity();
            updateFormSummary();
        });
    });

    // Fonction de validation d'un champ
    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        let isValid = true;

        // Réinitialiser les classes
        field.classList.remove('is-valid', 'is-invalid');

        if (isRequired && !value) {
            isValid = false;
        } else if (value) {
            // Validations spécifiques
            switch (field.name) {
                case 'first_name':
                case 'last_name':
                    isValid = value.length >= 2 && /^[a-zA-ZÀ-ÿ\s\-']+$/.test(value);
                    break;
                case 'email':
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    isValid = emailPattern.test(value);
                    break;
                case 'phone':
                    const phonePattern = /^[+]?[0-9\s\-\(\)]+$/;
                    isValid = phonePattern.test(value) && value.replace(/\D/g, '').length >= 8;
                    break;
                case 'license_number':
                    isValid = value.length >= 6 && /^[A-Z0-9]+$/i.test(value);
                    break;
                case 'license_expiry':
                    const selectedDate = new Date(value);
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    isValid = selectedDate > tomorrow;
                    break;
            }
        }

        // Appliquer les classes de validation
        if (value && isValid) {
            field.classList.add('is-valid');
        } else if (value && !isValid) {
            field.classList.add('is-invalid');
        }

        return isValid;
    }

    // Fonction pour mettre à jour l'aperçu du nom
    function updateNamePreview() {
        const firstName = document.getElementById('first_name').value.trim();
        const lastName = document.getElementById('last_name').value.trim();

        if (firstName || lastName) {
            const fullName = `${firstName} ${lastName}`.trim();
            const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();

            fullNamePreview.textContent = fullName || 'Nom incomplet';
            avatarPreview.textContent = initials || '--';
            namePreview.style.display = 'block';
        } else {
            namePreview.style.display = 'none';
        }
    }

    // Fonction pour mettre à jour la validité du permis
    function updateLicenseValidity() {
        const licenseExpiry = document.getElementById('license_expiry').value;

        if (licenseExpiry) {
            const expiryDate = new Date(licenseExpiry);
            const today = new Date();
            const diffTime = expiryDate - today;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // Réinitialiser les classes
            licenseValidityAlert.classList.remove('alert-success', 'alert-warning', 'alert-danger');
            licenseValidityAlert.classList.remove('license-validity-valid', 'license-validity-warning', 'license-validity-danger');

            if (diffDays <= 0) {
                licenseValidityAlert.classList.add('alert-danger', 'license-validity-danger');
                licenseValidityText.textContent = 'Permis expiré';
                licenseValidityDetails.textContent = 'Ce permis a expiré. Veuillez vérifier la date.';
            } else if (diffDays <= 30) {
                licenseValidityAlert.classList.add('alert-warning', 'license-validity-warning');
                licenseValidityText.textContent = 'Expire bientôt';
                licenseValidityDetails.textContent = `Ce permis expire dans ${diffDays} jour(s).`;
            } else if (diffDays <= 90) {
                licenseValidityAlert.classList.add('alert-warning', 'license-validity-warning');
                licenseValidityText.textContent = 'Valide (attention)';
                licenseValidityDetails.textContent = `Ce permis expire dans ${diffDays} jour(s). Prévoir le renouvellement.`;
            } else {
                licenseValidityAlert.classList.add('alert-success', 'license-validity-valid');
                licenseValidityText.textContent = 'Valide';
                licenseValidityDetails.textContent = `Ce permis est valide pour encore ${diffDays} jour(s).`;
            }

            licenseValidityAlert.style.display = 'block';
        } else {
            licenseValidityAlert.style.display = 'none';
        }
    }

    // Fonction pour mettre à jour le résumé
    function updateFormSummary() {
        const formData = new FormData(form);
        const firstName = formData.get('first_name');
        const lastName = formData.get('last_name');
        const email = formData.get('email');
        const phone = formData.get('phone');
        const licenseNumber = formData.get('license_number');
        const licenseExpiry = formData.get('license_expiry');
        const status = formData.get('status');

        if (firstName || lastName || email) {
            const statusLabels = {
                'available': '✅ Disponible',
                'unavailable': '❌ Indisponible'
            };

            const fullName = `${firstName} ${lastName}`.trim();
            const licenseExpiryFormatted = licenseExpiry ? new Date(licenseExpiry).toLocaleDateString('fr-FR') : 'Non définie';

            summaryContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Chauffeur:</strong> ${fullName}<br>
                        <strong>Email:</strong> ${email}<br>
                        <strong>Téléphone:</strong> ${phone}
                    </div>
                    <div class="col-md-6">
                        <strong>N° Permis:</strong> ${licenseNumber}<br>
                        <strong>Expiration:</strong> ${licenseExpiryFormatted}<br>
                        <strong>Statut:</strong> ${statusLabels[status] || 'Non défini'}
                    </div>
                </div>
            `;
            formSummary.style.display = 'block';
        } else {
            formSummary.style.display = 'none';
        }
    }

    // Bouton aperçu
    previewBtn.addEventListener('click', function() {
        updateFormSummary();
        if (formSummary.style.display === 'none') {
            alert('Veuillez remplir au moins les informations de base pour voir l\'aperçu.');
        } else {
            formSummary.scrollIntoView({ behavior: 'smooth' });
        }
    });

    // Bouton réinitialiser
    resetBtn.addEventListener('click', function() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ? Toutes les données saisies seront perdues.')) {
            form.reset();
            inputs.forEach(input => {
                input.classList.remove('is-valid', 'is-invalid');
            });
            formSummary.style.display = 'none';
            namePreview.style.display = 'none';
            licenseValidityAlert.style.display = 'none';
            // Remettre le statut par défaut
            document.getElementById('status_available').checked = true;
        }
    });

    // Formatage automatique du téléphone
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.startsWith('33')) {
            value = '+' + value;
        } else if (value.startsWith('0')) {
            value = '+33' + value.substring(1);
        }
        // Formatage français
        if (value.startsWith('+33')) {
            value = value.replace(/(\+33)(\d{1})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4 $5 $6');
        }
        this.value = value;
    });

    // Formatage automatique du numéro de permis
    const licenseInput = document.getElementById('license_number');
    licenseInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    });

    // Soumission du formulaire
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validation complète
        let isFormValid = true;
        inputs.forEach(input => {
            if (!validateField(input)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            alert('Veuillez corriger les erreurs dans le formulaire avant de continuer.');
            return;
        }

        // Afficher l'indicateur de chargement
        const btnText = submitBtn.querySelector('.btn-text');
        const spinner = submitBtn.querySelector('.spinner-border');

        btnText.textContent = 'Enregistrement...';
        spinner.style.display = 'inline-block';
        submitBtn.disabled = true;

        // Soumettre le formulaire
        setTimeout(() => {
            form.submit();
        }, 500);
    });

    // Animation d'entrée pour les cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Initialiser les aperçus
    updateNamePreview();
    updateLicenseValidity();
    updateFormSummary();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/drivers/create.blade.php ENDPATH**/ ?>